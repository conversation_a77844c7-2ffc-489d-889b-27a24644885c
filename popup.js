// SecureVPN Pro - Popup Script

class VPNPopup {
  constructor() {
    this.isConnected = false;
    this.isConnecting = false;
    this.currentServer = null;
    this.servers = [];
    this.init();
  }

  async init() {
    // Initialize localization first
    await i18n.init();
    this.updateUI();

    await this.loadServers();
    await this.loadSettings();
    await this.updateConnectionStatus();
    await this.updateIPInfo();
    this.setupEventListeners();
    await Theme.init();

    // Update UI every 5 seconds
    setInterval(() => {
      this.updateConnectionStatus();
      this.updateIPInfo();
    }, 5000);
  }

  async loadServers() {
    try {
      const response = await fetch(chrome.runtime.getURL('servers.json'));
      const data = await response.json();
      this.servers = data.servers;
      this.renderServerList();
    } catch (error) {
      console.error('Failed to load servers:', error);
      this.showError('Failed to load server list');
    }
  }

  async loadSettings() {
    const settings = {
      autoConnect: await Storage.get('autoConnect') || false,
      blockWebRTC: await Storage.get('blockWebRTC') || true,
      dnsProtection: await Storage.get('dnsProtection') || true,
      selectedServer: await Storage.get('selectedServer') || null
    };

    // Update UI
    document.getElementById('autoConnect').checked = settings.autoConnect;
    document.getElementById('blockWebRTC').checked = settings.blockWebRTC;
    document.getElementById('dnsProtection').checked = settings.dnsProtection;

    // Set selected server
    if (settings.selectedServer) {
      this.currentServer = this.servers.find(s => s.id === settings.selectedServer);
      this.updateSelectedServer();
    }
  }

  setupEventListeners() {
    // Connect button
    document.getElementById('connectBtn').addEventListener('click', () => {
      this.toggleConnection();
    });

    // Theme toggle
    document.getElementById('themeToggle').addEventListener('click', () => {
      Theme.toggle();
    });

    // Refresh servers
    document.getElementById('refreshServers').addEventListener('click', () => {
      this.refreshServers();
    });

    // Settings
    document.getElementById('autoConnect').addEventListener('change', (e) => {
      Storage.set('autoConnect', e.target.checked);
    });

    document.getElementById('blockWebRTC').addEventListener('change', (e) => {
      Storage.set('blockWebRTC', e.target.checked);
      this.sendMessage({ action: 'updateWebRTCBlocking', enabled: e.target.checked });
    });

    document.getElementById('dnsProtection').addEventListener('change', (e) => {
      Storage.set('dnsProtection', e.target.checked);
      this.sendMessage({ action: 'updateDNSProtection', enabled: e.target.checked });
    });

    // Language selector
    document.getElementById('languageBtn').addEventListener('click', () => {
      this.toggleLanguageDropdown();
    });

    document.querySelectorAll('.language-option').forEach(option => {
      option.addEventListener('click', (e) => {
        const lang = e.currentTarget.dataset.lang;
        this.changeLanguage(lang);
      });
    });

    // Close language dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.language-selector')) {
        this.hideLanguageDropdown();
      }
    });

    // Language change event
    window.addEventListener('languageChanged', () => {
      this.updateUI();
      this.renderServerList();
    });

    // Footer buttons
    document.getElementById('aboutBtn').addEventListener('click', () => {
      this.showAbout();
    });

    document.getElementById('supportBtn').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://support.securevpn.pro' });
    });

    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.showAdvancedSettings();
    });
  }

  renderServerList() {
    const serverList = document.getElementById('serverList');
    serverList.innerHTML = '';

    this.servers.forEach(server => {
      const serverElement = this.createServerElement(server);
      serverList.appendChild(serverElement);
    });
  }

  createServerElement(server) {
    const div = document.createElement('div');
    div.className = 'server-item';
    div.dataset.serverId = server.id;

    if (this.currentServer && this.currentServer.id === server.id) {
      div.classList.add('selected');
    }

    // Translate server name and location
    const translatedCountry = t(`countries.${server.country}`) || server.country;
    const translatedCity = t(`cities.${server.city}`) || server.city;

    div.innerHTML = `
      <span class="server-flag">${server.flag}</span>
      <div class="server-info">
        <div class="server-name">${server.name}</div>
        <div class="server-location">${translatedCity}, ${translatedCountry}</div>
      </div>
      <div class="server-stats">
        <div class="server-ping">${i18n.formatNumber(server.ping)}ms</div>
        <div class="server-load">${i18n.formatNumber(server.load)}%</div>
      </div>
    `;

    div.addEventListener('click', () => {
      this.selectServer(server);
    });

    return div;
  }

  selectServer(server) {
    this.currentServer = server;
    Storage.set('selectedServer', server.id);
    this.updateSelectedServer();
  }

  updateSelectedServer() {
    // Update UI
    document.querySelectorAll('.server-item').forEach(item => {
      item.classList.remove('selected');
    });

    if (this.currentServer) {
      const selectedElement = document.querySelector(`[data-server-id="${this.currentServer.id}"]`);
      if (selectedElement) {
        selectedElement.classList.add('selected');
      }
    }
  }

  async toggleConnection() {
    if (this.isConnecting) return;

    if (this.isConnected) {
      await this.disconnect();
    } else {
      await this.connect();
    }
  }

  async connect() {
    if (!this.currentServer) {
      this.showError(t('selectServerFirst'));
      return;
    }

    this.setConnecting(true);

    try {
      const response = await this.sendMessage({
        action: 'connect',
        server: this.currentServer
      });

      if (response.success) {
        this.setConnected(true);
        if (response.demo) {
          this.showSuccess(t('demoConnection'));
        } else {
          await Notifications.showConnectionStatus(true, this.currentServer.name);
        }
      } else {
        throw new Error(response.error || t('connectionFailed'));
      }
    } catch (error) {
      console.error('Connection error:', error);
      this.showError('Failed to connect: ' + error.message);
    } finally {
      this.setConnecting(false);
    }
  }

  async disconnect() {
    this.setConnecting(true);

    try {
      const response = await this.sendMessage({ action: 'disconnect' });

      if (response.success) {
        this.setConnected(false);
        await Notifications.showConnectionStatus(false);
      } else {
        throw new Error(response.error || 'Disconnection failed');
      }
    } catch (error) {
      console.error('Disconnection error:', error);
      this.showError('Failed to disconnect: ' + error.message);
    } finally {
      this.setConnecting(false);
    }
  }

  setConnecting(connecting) {
    this.isConnecting = connecting;
    const connectBtn = document.getElementById('connectBtn');
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');

    if (connecting) {
      connectBtn.classList.add('connecting');
      connectBtn.querySelector('.btn-text').textContent = t('connecting');
      statusIndicator.classList.remove('connected');
      statusIndicator.classList.add('connecting');
      statusText.textContent = t('connecting');
    } else {
      connectBtn.classList.remove('connecting');
    }
  }

  setConnected(connected) {
    this.isConnected = connected;
    const connectBtn = document.getElementById('connectBtn');
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const currentServer = document.getElementById('currentServer');

    if (connected) {
      connectBtn.classList.add('connected');
      connectBtn.querySelector('.btn-text').textContent = t('disconnect');
      statusIndicator.classList.add('connected');
      statusIndicator.classList.remove('connecting');
      statusText.textContent = t('connected');
      currentServer.textContent = this.currentServer ? this.currentServer.name : '';
    } else {
      connectBtn.classList.remove('connected');
      connectBtn.querySelector('.btn-text').textContent = t('connect');
      statusIndicator.classList.remove('connected', 'connecting');
      statusText.textContent = t('disconnected');
      currentServer.textContent = '';
    }
  }

  async updateConnectionStatus() {
    try {
      const response = await this.sendMessage({ action: 'getStatus' });
      this.setConnected(response.connected);

      if (response.connected && response.server) {
        this.currentServer = response.server;
        this.updateSelectedServer();
      }
    } catch (error) {
      console.error('Failed to get connection status:', error);
    }
  }

  async updateIPInfo() {
    try {
      const ip = await Network.getPublicIP();
      const location = await Network.getLocation(ip);

      document.getElementById('ipAddress').textContent = ip;
      document.getElementById('location').textContent = `${location.city}, ${location.country}`;

      if (this.currentServer) {
        const ping = await Network.pingServer(this.currentServer.host, this.currentServer.port);
        document.getElementById('ping').textContent = Format.ping(ping);
      }
    } catch (error) {
      console.error('Failed to update IP info:', error);
    }
  }

  async refreshServers() {
    const refreshBtn = document.getElementById('refreshServers');
    refreshBtn.style.transform = 'rotate(360deg)';

    try {
      await this.loadServers();

      // Update ping for all servers
      for (const server of this.servers) {
        const ping = await Network.pingServer(server.host, server.port);
        server.ping = ping;
      }

      this.renderServerList();
    } catch (error) {
      console.error('Failed to refresh servers:', error);
      this.showError('Failed to refresh servers');
    } finally {
      setTimeout(() => {
        refreshBtn.style.transform = 'rotate(0deg)';
      }, 500);
    }
  }

  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, resolve);
    });
  }

  showError(message) {
    this.showToast(message, 'error');
  }

  showSuccess(message) {
    this.showToast(message, 'success');
  }

  showToast(message, type = 'info') {
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `${type}-toast`;
    toast.textContent = message;

    const colors = {
      error: 'var(--danger-color)',
      success: 'var(--success-color)',
      info: 'var(--primary-color)'
    };

    toast.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: ${colors[type]};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 1000;
      animation: slideDown 0.3s ease;
      max-width: 300px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 3000);
  }

  showAbout() {
    alert('SecureVPN Pro v1.0.0\n\nA professional VPN extension with advanced security features.\n\nDeveloped with ❤️ for your privacy and security.');
  }

  showAdvancedSettings() {
    // This could open a new tab with advanced settings
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
  }

  // Language control functions
  toggleLanguageDropdown() {
    const dropdown = document.getElementById('languageDropdown');
    dropdown.classList.toggle('show');
  }

  hideLanguageDropdown() {
    const dropdown = document.getElementById('languageDropdown');
    dropdown.classList.remove('show');
  }

  async changeLanguage(lang) {
    await i18n.setLanguage(lang);
    this.hideLanguageDropdown();
    this.updateLanguageButton();
  }

  updateLanguageButton() {
    const currentLangText = document.getElementById('currentLangText');
    const currentLang = i18n.getCurrentLanguage();

    if (currentLang === 'ar') {
      currentLangText.textContent = 'ع';
    } else {
      currentLangText.textContent = 'EN';
    }

    // Update active language option
    document.querySelectorAll('.language-option').forEach(option => {
      option.classList.remove('active');
      if (option.dataset.lang === currentLang) {
        option.classList.add('active');
      }
    });
  }

  updateUI() {
    // Update all elements with data-i18n attributes
    document.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      element.textContent = t(key);
    });

    // Update title attributes
    document.querySelectorAll('[data-i18n-title]').forEach(element => {
      const key = element.getAttribute('data-i18n-title');
      element.title = t(key);
    });

    // Update language button
    this.updateLanguageButton();

    // Update connection status if needed
    if (this.isConnected !== undefined) {
      this.setConnected(this.isConnected);
    }
  }
}

// Initialize the popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new VPNPopup();
});
