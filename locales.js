// SecureVPN Pro - Localization System

class Localization {
  constructor() {
    this.currentLanguage = 'en';
    this.translations = {
      en: {
        // Header
        appName: 'SecureVPN Pro',
        
        // Connection Status
        connected: 'Connected',
        disconnected: 'Disconnected',
        connecting: 'Connecting...',
        connect: 'Connect',
        disconnect: 'Disconnect',
        
        // Server Selection
        selectServer: 'Select Server',
        refreshServers: 'Refresh Servers',
        serverStats: 'Server Statistics',
        ping: 'Ping',
        load: 'Load',
        
        // Statistics
        ipAddress: 'IP Address',
        location: 'Location',
        currentServer: 'Current Server',
        
        // Settings
        settings: 'Settings',
        autoConnect: 'Auto-connect on startup',
        blockWebRTC: 'Block WebRTC leaks',
        dnsProtection: 'DNS leak protection',
        
        // Footer
        about: 'About',
        support: 'Support',
        
        // Messages
        connectionSuccess: 'Successfully connected to',
        connectionFailed: 'Failed to connect',
        disconnectionSuccess: 'VPN disconnected',
        demoConnection: 'Connected to demo server! (No actual proxy)',
        selectServerFirst: 'Please select a server first',
        
        // Countries
        countries: {
          US: 'United States',
          UK: 'United Kingdom',
          DE: 'Germany',
          JP: 'Japan',
          SG: 'Singapore',
          LOCAL: 'Local'
        },
        
        // Cities
        cities: {
          'New York': 'New York',
          'Los Angeles': 'Los Angeles',
          'London': 'London',
          'Frankfurt': 'Frankfurt',
          'Tokyo': 'Tokyo',
          'Singapore': 'Singapore',
          'Localhost': 'Localhost'
        }
      },
      
      ar: {
        // Header
        appName: 'SecureVPN Pro',
        
        // Connection Status
        connected: 'متصل',
        disconnected: 'غير متصل',
        connecting: 'جاري الاتصال...',
        connect: 'اتصال',
        disconnect: 'قطع الاتصال',
        
        // Server Selection
        selectServer: 'اختيار الخادم',
        refreshServers: 'تحديث الخوادم',
        serverStats: 'إحصائيات الخادم',
        ping: 'زمن الاستجابة',
        load: 'الحمولة',
        
        // Statistics
        ipAddress: 'عنوان IP',
        location: 'الموقع',
        currentServer: 'الخادم الحالي',
        
        // Settings
        settings: 'الإعدادات',
        autoConnect: 'اتصال تلقائي عند البدء',
        blockWebRTC: 'حجب تسريبات WebRTC',
        dnsProtection: 'حماية تسريب DNS',
        
        // Footer
        about: 'حول',
        support: 'الدعم',
        
        // Messages
        connectionSuccess: 'تم الاتصال بنجاح إلى',
        connectionFailed: 'فشل في الاتصال',
        disconnectionSuccess: 'تم قطع اتصال VPN',
        demoConnection: 'تم الاتصال بالخادم التجريبي! (بدون بروكسي فعلي)',
        selectServerFirst: 'يرجى اختيار خادم أولاً',
        
        // Countries
        countries: {
          US: 'الولايات المتحدة',
          UK: 'المملكة المتحدة',
          DE: 'ألمانيا',
          JP: 'اليابان',
          SG: 'سنغافورة',
          LOCAL: 'محلي'
        },
        
        // Cities
        cities: {
          'New York': 'نيويورك',
          'Los Angeles': 'لوس أنجلوس',
          'London': 'لندن',
          'Frankfurt': 'فرانكفورت',
          'Tokyo': 'طوكيو',
          'Singapore': 'سنغافورة',
          'Localhost': 'المضيف المحلي'
        }
      }
    };
    
    this.init();
  }

  async init() {
    // Load saved language preference
    const savedLang = await this.getSavedLanguage();
    if (savedLang) {
      this.currentLanguage = savedLang;
    } else {
      // Auto-detect browser language
      const browserLang = navigator.language || navigator.userLanguage;
      if (browserLang.startsWith('ar')) {
        this.currentLanguage = 'ar';
      }
    }
    
    this.applyLanguage();
  }

  async getSavedLanguage() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get('language');
        return result.language;
      }
      return localStorage.getItem('vpn_language');
    } catch (error) {
      return null;
    }
  }

  async saveLanguage(lang) {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ language: lang });
      } else {
        localStorage.setItem('vpn_language', lang);
      }
    } catch (error) {
      console.error('Failed to save language:', error);
    }
  }

  translate(key, params = {}) {
    const keys = key.split('.');
    let value = this.translations[this.currentLanguage];
    
    for (const k of keys) {
      if (value && typeof value === 'object') {
        value = value[k];
      } else {
        break;
      }
    }
    
    // Fallback to English if translation not found
    if (!value) {
      value = this.translations.en;
      for (const k of keys) {
        if (value && typeof value === 'object') {
          value = value[k];
        } else {
          break;
        }
      }
    }
    
    // Replace parameters
    if (typeof value === 'string' && Object.keys(params).length > 0) {
      Object.keys(params).forEach(param => {
        value = value.replace(`{${param}}`, params[param]);
      });
    }
    
    return value || key;
  }

  async setLanguage(lang) {
    if (this.translations[lang]) {
      this.currentLanguage = lang;
      await this.saveLanguage(lang);
      this.applyLanguage();
      
      // Trigger language change event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('languageChanged', { 
          detail: { language: lang } 
        }));
      }
    }
  }

  applyLanguage() {
    // Set document direction
    if (typeof document !== 'undefined') {
      document.documentElement.dir = this.currentLanguage === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = this.currentLanguage;
      
      // Add language class to body
      document.body.classList.remove('lang-en', 'lang-ar');
      document.body.classList.add(`lang-${this.currentLanguage}`);
    }
  }

  getCurrentLanguage() {
    return this.currentLanguage;
  }

  isRTL() {
    return this.currentLanguage === 'ar';
  }

  getAvailableLanguages() {
    return [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'ar', name: 'Arabic', nativeName: 'العربية' }
    ];
  }

  formatNumber(number) {
    if (this.currentLanguage === 'ar') {
      // Convert to Arabic-Indic numerals
      const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
      return number.toString().replace(/[0-9]/g, (digit) => arabicNumerals[digit]);
    }
    return number.toString();
  }

  formatDirection(text) {
    if (this.currentLanguage === 'ar') {
      return `\u202B${text}\u202C`; // Right-to-left embedding
    }
    return text;
  }
}

// Global instance
const i18n = new Localization();

// Helper function for easy translation
function t(key, params = {}) {
  return i18n.translate(key, params);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { Localization, i18n, t };
}
