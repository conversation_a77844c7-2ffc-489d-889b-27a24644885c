# تعليمات التثبيت والاستخدام - SecureVPN Pro

## 📋 متطلبات النظام

### متطلبات المتصفح
- **Google Chrome**: الإصدار 88 أو أحدث
- **Microsoft Edge**: الإصدار 88 أو أحدث  
- **Brave Browser**: أي إصدار حديث
- **Opera**: الإصدار 74 أو أحدث

### متطلبات النظام
- **Windows**: 10 أو أحدث
- **macOS**: 10.14 أو أحدث
- **Linux**: Ubuntu 18.04+ أو توزيعات مماثلة
- **ذاكرة الوصول العشوائي**: 4 جيجابايت على الأقل
- **مساحة القرص**: 50 ميجابايت

## 🚀 طريقة التثبيت

### الطريقة الأولى: تحميل مباشر (موصى بها)

#### الخطوة 1: تحميل الملفات
```bash
# استنساخ المستودع
git clone https://github.com/your-repo/securevpn-pro.git

# أو تحميل ملف ZIP
# قم بتحميل الملف من GitHub وفك الضغط
```

#### الخطوة 2: فتح صفحة الإضافات في Chrome
1. افتح متصفح Google Chrome
2. اكتب في شريط العناوين: `chrome://extensions/`
3. اضغط Enter

#### الخطوة 3: تفعيل وضع المطور
1. في الزاوية العلوية اليمنى، فعّل "Developer mode"
2. ستظهر أزرار جديدة في الأعلى

#### الخطوة 4: تحميل الإضافة
1. اضغط على "Load unpacked"
2. اختر مجلد الإضافة الذي حملته
3. اضغط "Select Folder"

#### الخطوة 5: تثبيت الأيقونة
1. اضغط على أيقونة الأحجية 🧩 في شريط أدوات Chrome
2. ابحث عن "SecureVPN Pro"
3. اضغط على أيقونة الدبوس 📌 لتثبيتها

### الطريقة الثانية: من Chrome Web Store (قريباً)
ستكون الإضافة متاحة على متجر Chrome قريباً بعد المراجعة.

## ⚙️ الإعداد الأولي

### إعداد الخوادم

#### استخدام الخوادم الافتراضية
الإضافة تأتي مع خوادم تجريبية مُعدة مسبقاً في ملف `servers.json`.

#### إضافة خوادم مخصصة
1. افتح ملف `servers.json`
2. أضف خادمك الجديد:

```json
{
  "id": "my-server-1",
  "name": "خادمي المخصص",
  "country": "US",
  "city": "نيويورك",
  "flag": "🇺🇸",
  "host": "my-vpn-server.com",
  "port": 1080,
  "type": "socks5",
  "username": "my-username",
  "password": "my-password",
  "ping": 45,
  "load": 23
}
```

#### أنواع البروكسي المدعومة
- **SOCKS5**: `"type": "socks5"` (موصى به)
- **HTTP**: `"type": "http"`

### إعداد الأمان

#### حماية WebRTC (افتراضي: مُفعّل)
- يمنع تسرب عنوان IP الحقيقي
- يحجب طلبات STUN/TURN

#### حماية DNS (افتراضي: مُفعّل)  
- يوجه استعلامات DNS عبر خوادم آمنة
- يستخدم Cloudflare DNS (*******)

#### الاتصال التلقائي (افتراضي: مُعطّل)
- يتصل تلقائياً عند بدء تشغيل المتصفح
- يستخدم آخر خادم محدد

## 🎯 دليل الاستخدام

### الاتصال الأساسي

#### الخطوة 1: اختيار الخادم
1. اضغط على أيقونة SecureVPN Pro
2. ستظهر قائمة بالخوادم المتاحة
3. اختر الخادم المناسب حسب:
   - **الموقع**: اختر الأقرب لك للسرعة
   - **Ping**: الأقل أفضل (أقل من 100ms مثالي)
   - **Load**: الأقل أفضل (أقل من 50% مثالي)

#### الخطوة 2: الاتصال
1. اضغط على زر "Connect"
2. انتظر حتى يتغير الزر إلى "Disconnect"
3. ستظهر نقطة خضراء تشير للاتصال الناجح

#### الخطوة 3: التحقق من الاتصال
1. زر موقع https://whatismyipaddress.com/
2. تأكد من تغير عنوان IP
3. تحقق من الموقع الجغرافي الجديد

### الميزات المتقدمة

#### تبديل الخوادم
- يمكن تبديل الخوادم أثناء الاتصال
- الاتصال الجديد يحل محل القديم تلقائياً
- لا حاجة لقطع الاتصال أولاً

#### الوضع الليلي
- اضغط على أيقونة الشمس/القمر في الأعلى
- يحفظ التفضيل تلقائياً
- يطبق على جميع النوافذ

#### تحديث قائمة الخوادم
- اضغط على أيقونة التحديث 🔄
- يختبر ping جميع الخوادم
- يحدث معلومات الحمولة

### إعدادات الأمان

#### حماية WebRTC
```
☑️ Block WebRTC leaks
```
- **مُفعّل**: يحجب تسرب IP عبر WebRTC
- **مُعطّل**: يسمح بـ WebRTC (غير آمن)

#### حماية DNS  
```
☑️ DNS leak protection
```
- **مُفعّل**: يوجه DNS عبر خوادم آمنة
- **مُعطّل**: يستخدم DNS الافتراضي

#### الاتصال التلقائي
```
☐ Auto-connect on startup
```
- **مُفعّل**: يتصل تلقائياً عند بدء Chrome
- **مُعطّل**: يتطلب اتصال يدوي

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### فشل الاتصال
**الأعراض**: رسالة "Connection failed"

**الحلول**:
1. **تحقق من بيانات الخادم**:
   - تأكد من صحة اسم المستخدم وكلمة المرور
   - تحقق من عنوان الخادم والمنفذ

2. **اختبر الاتصال**:
   ```bash
   # اختبار SOCKS5
   curl --socks5 username:password@server:port https://httpbin.org/ip
   
   # اختبار HTTP
   curl --proxy username:password@server:port https://httpbin.org/ip
   ```

3. **جرب خادم آخر**:
   - اختر خادم مختلف من القائمة
   - تحقق من حالة الخادم

#### بطء في التصفح
**الأعراض**: تحميل بطيء للصفحات

**الحلول**:
1. **اختر خادم أقرب**:
   - استخدم خادم في نفس القارة
   - تحقق من ping time (أقل من 100ms)

2. **تحقق من حمولة الخادم**:
   - اختر خادم بحمولة أقل من 50%
   - جرب في أوقات مختلفة

3. **عطّل الحماية الإضافية**:
   - عطّل WebRTC protection مؤقتاً
   - عطّل DNS protection إذا لم تكن ضرورية

#### الإضافة لا تعمل
**الأعراض**: الإضافة لا تظهر أو لا تستجيب

**الحلول**:
1. **إعادة تحميل الإضافة**:
   - اذهب إلى `chrome://extensions/`
   - اضغط على أيقونة التحديث 🔄 بجانب الإضافة

2. **تحقق من الأخطاء**:
   - اضغط F12 لفتح Developer Tools
   - اذهب إلى تبويب Console
   - ابحث عن رسائل خطأ

3. **إعادة تثبيت**:
   - احذف الإضافة
   - أعد تحميلها من المجلد

### وضع التشخيص

#### تفعيل السجلات المفصلة
1. افتح Developer Tools (F12)
2. اذهب إلى Console
3. ابحث عن رسائل تبدأ بـ `[SecureVPN]`

#### اختبار الاتصال يدوياً
```javascript
// في Console، اختبر الاتصال
chrome.runtime.sendMessage({action: 'getStatus'}, console.log);
```

#### فحص إعدادات البروكسي
```javascript
// فحص إعدادات البروكسي الحالية
chrome.proxy.settings.get({}, console.log);
```

## 🛡️ نصائح الأمان

### أفضل الممارسات

#### اختيار الخوادم
- **استخدم خوادم موثوقة فقط**
- **تجنب الخوادم المجانية المشكوك فيها**
- **غيّر الخوادم بانتظام**

#### حماية البيانات
- **لا تدخل معلومات حساسة على مواقع غير مشفرة**
- **استخدم HTTPS دائماً**
- **فعّل جميع ميزات الحماية**

#### مراقبة الاتصال
- **تحقق من IP address بانتظام**
- **راقب سرعة الاتصال**
- **انتبه لرسائل التحذير**

### التحقق من الأمان

#### اختبار تسرب IP
1. **WebRTC**: https://browserleaks.com/webrtc
2. **DNS**: https://dnsleaktest.com/
3. **IP العام**: https://whatismyipaddress.com/

#### اختبار الحماية
```bash
# اختبار من Terminal/Command Prompt
nslookup google.com
# يجب أن يظهر DNS server مختلف
```

## 📞 الدعم والمساعدة

### قنوات الدعم
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [إنشاء تذكرة](https://github.com/your-repo/securevpn-pro/issues)
- **الوثائق**: [Wiki](https://github.com/your-repo/securevpn-pro/wiki)

### معلومات مفيدة عند طلب المساعدة
1. **إصدار Chrome**: `chrome://version/`
2. **إصدار الإضافة**: من صفحة الإضافات
3. **نظام التشغيل**: Windows/Mac/Linux
4. **رسالة الخطأ**: نسخ كامل للرسالة
5. **خطوات إعادة الإنتاج**: ما فعلته قبل المشكلة

### الأسئلة الشائعة

**س: هل الإضافة مجانية؟**
ج: نعم، الإضافة مفتوحة المصدر ومجانية تماماً.

**س: هل تحفظون سجلات التصفح؟**
ج: لا، الإضافة لا تحفظ أي سجلات أو بيانات شخصية.

**س: هل يمكن استخدامها مع VPN آخر؟**
ج: لا ينصح بذلك، قد يحدث تضارب في الإعدادات.

**س: لماذا بعض المواقع لا تعمل؟**
ج: بعض المواقع تحجب البروكسي، جرب خادم آخر.

---

**⚠️ تنبيه مهم**: تأكد من الامتثال للقوانين المحلية وشروط الخدمة عند استخدام خدمات VPN.
