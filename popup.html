<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureVPN Pro</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="icons/icon32.png" alt="SecureVPN Pro" class="logo-icon">
                <h1 data-i18n="appName">SecureVPN Pro</h1>
            </div>
            <div class="header-controls">
                <div class="language-selector">
                    <button id="languageBtn" class="language-btn" title="Change Language">
                        <span id="currentLangText">EN</span>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 10l5 5 5-5z"/>
                        </svg>
                    </button>
                    <div id="languageDropdown" class="language-dropdown">
                        <div class="language-option" data-lang="en">
                            <span>🇺🇸 English</span>
                        </div>
                        <div class="language-option" data-lang="ar">
                            <span>🇸🇦 العربية</span>
                        </div>
                    </div>
                </div>
                <button id="themeToggle" class="theme-btn" title="Toggle Dark Mode">
                    <svg class="theme-icon" viewBox="0 0 24 24">
                        <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                    </svg>
                </button>
            </div>
        </header>

        <!-- Connection Status -->
        <div class="status-section">
            <div class="connection-status" id="connectionStatus">
                <div class="status-indicator" id="statusIndicator"></div>
                <div class="status-text">
                    <span id="statusText" data-i18n="disconnected">Disconnected</span>
                    <span id="currentServer" class="current-server"></span>
                </div>
            </div>
            <button id="connectBtn" class="connect-btn">
                <span class="btn-text" data-i18n="connect">Connect</span>
                <div class="btn-loader"></div>
            </button>
        </div>

        <!-- Quick Stats -->
        <div class="stats-section">
            <div class="stat-item">
                <span class="stat-label" data-i18n="ipAddress">IP Address</span>
                <span id="ipAddress" class="stat-value">Loading...</span>
            </div>
            <div class="stat-item">
                <span class="stat-label" data-i18n="location">Location</span>
                <span id="location" class="stat-value">Unknown</span>
            </div>
            <div class="stat-item">
                <span class="stat-label" data-i18n="ping">Ping</span>
                <span id="ping" class="stat-value">--</span>
            </div>
        </div>

        <!-- Server Selection -->
        <div class="server-section">
            <div class="section-header">
                <h3 data-i18n="selectServer">Select Server</h3>
                <button id="refreshServers" class="refresh-btn" data-i18n-title="refreshServers">
                    <svg viewBox="0 0 24 24">
                        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                </button>
            </div>
            <div class="server-list" id="serverList">
                <!-- Servers will be populated by JavaScript -->
            </div>
        </div>

        <!-- Settings -->
        <div class="settings-section">
            <div class="setting-item">
                <label class="setting-label">
                    <input type="checkbox" id="autoConnect" class="setting-checkbox">
                    <span class="checkmark"></span>
                    <span data-i18n="autoConnect">Auto-connect on startup</span>
                </label>
            </div>
            <div class="setting-item">
                <label class="setting-label">
                    <input type="checkbox" id="blockWebRTC" class="setting-checkbox" checked>
                    <span class="checkmark"></span>
                    <span data-i18n="blockWebRTC">Block WebRTC leaks</span>
                </label>
            </div>
            <div class="setting-item">
                <label class="setting-label">
                    <input type="checkbox" id="dnsProtection" class="setting-checkbox" checked>
                    <span class="checkmark"></span>
                    <span data-i18n="dnsProtection">DNS leak protection</span>
                </label>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-links">
                <button id="aboutBtn" class="footer-btn" data-i18n="about">About</button>
                <button id="supportBtn" class="footer-btn" data-i18n="support">Support</button>
                <button id="settingsBtn" class="footer-btn" data-i18n="settings">Settings</button>
            </div>
        </footer>
    </div>

    <script src="locales.js"></script>
    <script src="utils.js"></script>
    <script src="popup.js"></script>
</body>
</html>
