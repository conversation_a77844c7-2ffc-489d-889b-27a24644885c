#!/usr/bin/env node

// Package script for SecureVPN Pro extension

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class ExtensionPackager {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.outputDir = path.join(this.projectRoot, 'dist');
    this.packageName = 'securevpn-pro';
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '✓',
      'warning': '⚠',
      'error': '✗'
    }[type];
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async createOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      this.log('Created output directory');
    }
  }

  getFilesToInclude() {
    return [
      'manifest.json',
      'background.js',
      'popup.html',
      'popup.js',
      'content.js',
      'utils.js',
      'styles.css',
      'servers.json',
      'rules.json',
      'settings.html',
      'icons/',
      'LICENSE',
      'README.md',
      'INSTALLATION.md',
      'QUICK_START.md'
    ];
  }

  getFilesToExclude() {
    return [
      'node_modules/',
      'scripts/',
      'dist/',
      '.git/',
      '.gitignore',
      'package.json',
      'package-lock.json',
      '*.log',
      '.DS_Store',
      'Thumbs.db'
    ];
  }

  async validateFiles() {
    this.log('Validating files before packaging...');
    
    const requiredFiles = [
      'manifest.json',
      'background.js',
      'popup.html',
      'popup.js',
      'content.js',
      'utils.js',
      'styles.css',
      'servers.json',
      'rules.json'
    ];

    const missingFiles = [];
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.projectRoot, file);
      if (!fs.existsSync(filePath)) {
        missingFiles.push(file);
      }
    }

    if (missingFiles.length > 0) {
      throw new Error(`Missing required files: ${missingFiles.join(', ')}`);
    }

    this.log('All required files found');
  }

  async createZipPackage() {
    this.log('Creating ZIP package...');
    
    const version = this.getVersion();
    const zipPath = path.join(this.outputDir, `${this.packageName}-v${version}.zip`);
    
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // Maximum compression
      });

      output.on('close', () => {
        this.log(`ZIP package created: ${zipPath} (${archive.pointer()} bytes)`);
        resolve(zipPath);
      });

      archive.on('error', (err) => {
        reject(err);
      });

      archive.pipe(output);

      // Add files to archive
      const filesToInclude = this.getFilesToInclude();
      
      for (const file of filesToInclude) {
        const filePath = path.join(this.projectRoot, file);
        
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          
          if (stats.isDirectory()) {
            archive.directory(filePath, file);
            this.log(`Added directory: ${file}`);
          } else {
            archive.file(filePath, { name: file });
            this.log(`Added file: ${file}`);
          }
        }
      }

      archive.finalize();
    });
  }

  async createCRXPackage() {
    this.log('CRX packaging requires Chrome Web Store developer tools');
    this.log('Please use Chrome Developer Dashboard to create CRX package');
    
    // Create instructions file
    const instructionsPath = path.join(this.outputDir, 'CRX_INSTRUCTIONS.md');
    const instructions = `# Creating CRX Package

To create a CRX package for Chrome Web Store:

1. Go to Chrome Developer Dashboard: https://chrome.google.com/webstore/devconsole/
2. Click "Add new item"
3. Upload the ZIP file: ${this.packageName}-v${this.getVersion()}.zip
4. Fill in the required information:
   - Name: SecureVPN Pro
   - Description: Professional VPN extension with advanced security features
   - Category: Productivity
   - Language: English
5. Upload screenshots and promotional images
6. Set privacy policy and permissions
7. Submit for review

## Required Images:
- Icon: 128x128 pixels
- Small promotional tile: 440x280 pixels  
- Large promotional tile: 920x680 pixels
- Marquee promotional tile: 1400x560 pixels
- Screenshots: 1280x800 or 640x400 pixels

## Privacy Policy:
Include information about:
- No logging of browsing activity
- Local storage of preferences only
- Use of proxy protocols
- Security features implemented
`;

    fs.writeFileSync(instructionsPath, instructions);
    this.log('Created CRX instructions file');
  }

  getVersion() {
    try {
      const manifestPath = path.join(this.projectRoot, 'manifest.json');
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      return manifest.version || '1.0.0';
    } catch (error) {
      this.log('Could not read version from manifest, using default', 'warning');
      return '1.0.0';
    }
  }

  async generateChecksums(filePath) {
    const crypto = require('crypto');
    const fileBuffer = fs.readFileSync(filePath);
    
    const md5 = crypto.createHash('md5').update(fileBuffer).digest('hex');
    const sha256 = crypto.createHash('sha256').update(fileBuffer).digest('hex');
    
    const checksumPath = filePath + '.checksums';
    const checksumContent = `MD5: ${md5}
SHA256: ${sha256}
File: ${path.basename(filePath)}
Size: ${fileBuffer.length} bytes
Created: ${new Date().toISOString()}
`;

    fs.writeFileSync(checksumPath, checksumContent);
    this.log(`Generated checksums: ${checksumPath}`);
  }

  async createManifestInfo() {
    this.log('Creating manifest information...');
    
    const manifestPath = path.join(this.projectRoot, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    const info = {
      name: manifest.name,
      version: manifest.version,
      description: manifest.description,
      permissions: manifest.permissions,
      manifest_version: manifest.manifest_version,
      packaged_at: new Date().toISOString(),
      files_included: this.getFilesToInclude(),
      chrome_minimum_version: '88'
    };

    const infoPath = path.join(this.outputDir, 'package-info.json');
    fs.writeFileSync(infoPath, JSON.stringify(info, null, 2));
    this.log('Created package information file');
  }

  async run() {
    try {
      this.log('Starting extension packaging...');
      
      await this.createOutputDir();
      await this.validateFiles();
      
      const zipPath = await this.createZipPackage();
      await this.generateChecksums(zipPath);
      await this.createCRXPackage();
      await this.createManifestInfo();
      
      this.log('\n=== Packaging Complete ===');
      this.log(`Package: ${path.basename(zipPath)}`);
      this.log(`Version: ${this.getVersion()}`);
      this.log(`Output directory: ${this.outputDir}`);
      this.log('\nNext steps:');
      this.log('1. Test the extension by loading the ZIP file');
      this.log('2. Review the CRX instructions for Chrome Web Store submission');
      this.log('3. Prepare promotional materials and screenshots');
      
      return true;
      
    } catch (error) {
      this.log(`Packaging failed: ${error.message}`, 'error');
      return false;
    }
  }
}

// Run packaging if called directly
if (require.main === module) {
  const packager = new ExtensionPackager();
  packager.run().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = ExtensionPackager;
