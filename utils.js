// Utility functions for SecureVPN Pro

/**
 * Storage utilities
 */
const Storage = {
  async get(key) {
    try {
      const result = await chrome.storage.local.get(key);
      return result[key];
    } catch (error) {
      console.error('Storage get error:', error);
      return null;
    }
  },

  async set(key, value) {
    try {
      await chrome.storage.local.set({ [key]: value });
      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      return false;
    }
  },

  async remove(key) {
    try {
      await chrome.storage.local.remove(key);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      return false;
    }
  },

  async clear() {
    try {
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('Storage clear error:', error);
      return false;
    }
  }
};

/**
 * Network utilities
 */
const Network = {
  async getPublicIP() {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.error('Failed to get public IP:', error);
      return 'Unknown';
    }
  },

  async getLocation(ip) {
    try {
      const response = await fetch(`https://ipapi.co/${ip}/json/`);
      const data = await response.json();
      return {
        country: data.country_name || 'Unknown',
        city: data.city || 'Unknown',
        region: data.region || 'Unknown'
      };
    } catch (error) {
      console.error('Failed to get location:', error);
      return { country: 'Unknown', city: 'Unknown', region: 'Unknown' };
    }
  },

  async pingServer(host, port) {
    try {
      const startTime = Date.now();
      const response = await fetch(`https://${host}:${port}`, {
        method: 'HEAD',
        mode: 'no-cors'
      });
      const endTime = Date.now();
      return endTime - startTime;
    } catch (error) {
      return 999; // Return high ping on error
    }
  }
};

/**
 * Theme utilities
 */
const Theme = {
  async init() {
    const savedTheme = await Storage.get('theme') || 'light';
    this.apply(savedTheme);
  },

  apply(theme) {
    document.documentElement.setAttribute('data-theme', theme);
  },

  async toggle() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    this.apply(newTheme);
    await Storage.set('theme', newTheme);
    return newTheme;
  }
};

/**
 * Notification utilities
 */
const Notifications = {
  async show(title, message, type = 'basic') {
    try {
      await chrome.notifications.create({
        type: type,
        iconUrl: 'icons/icon48.png',
        title: title,
        message: message
      });
    } catch (error) {
      console.error('Notification error:', error);
    }
  },

  async showConnectionStatus(connected, serverName = '') {
    const title = connected ? 'VPN Connected' : 'VPN Disconnected';
    const message = connected 
      ? `Successfully connected to ${serverName}`
      : 'VPN connection has been terminated';
    
    await this.show(title, message);
  }
};

/**
 * Validation utilities
 */
const Validator = {
  isValidIP(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  },

  isValidPort(port) {
    const portNum = parseInt(port);
    return portNum >= 1 && portNum <= 65535;
  },

  isValidHost(host) {
    const hostRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    return hostRegex.test(host) || this.isValidIP(host);
  }
};

/**
 * Animation utilities
 */
const Animation = {
  fadeIn(element, duration = 300) {
    element.style.opacity = '0';
    element.style.display = 'block';
    
    let start = null;
    function animate(timestamp) {
      if (!start) start = timestamp;
      const progress = timestamp - start;
      const opacity = Math.min(progress / duration, 1);
      
      element.style.opacity = opacity;
      
      if (progress < duration) {
        requestAnimationFrame(animate);
      }
    }
    
    requestAnimationFrame(animate);
  },

  fadeOut(element, duration = 300) {
    let start = null;
    const initialOpacity = parseFloat(getComputedStyle(element).opacity);
    
    function animate(timestamp) {
      if (!start) start = timestamp;
      const progress = timestamp - start;
      const opacity = Math.max(initialOpacity - (progress / duration), 0);
      
      element.style.opacity = opacity;
      
      if (progress < duration) {
        requestAnimationFrame(animate);
      } else {
        element.style.display = 'none';
      }
    }
    
    requestAnimationFrame(animate);
  },

  slideDown(element, duration = 300) {
    element.style.height = '0px';
    element.style.overflow = 'hidden';
    element.style.display = 'block';
    
    const targetHeight = element.scrollHeight;
    let start = null;
    
    function animate(timestamp) {
      if (!start) start = timestamp;
      const progress = timestamp - start;
      const height = Math.min((progress / duration) * targetHeight, targetHeight);
      
      element.style.height = height + 'px';
      
      if (progress < duration) {
        requestAnimationFrame(animate);
      } else {
        element.style.height = 'auto';
        element.style.overflow = 'visible';
      }
    }
    
    requestAnimationFrame(animate);
  }
};

/**
 * Debounce utility
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Format utilities
 */
const Format = {
  ping(ms) {
    if (ms < 50) return `${ms}ms (Excellent)`;
    if (ms < 100) return `${ms}ms (Good)`;
    if (ms < 200) return `${ms}ms (Fair)`;
    return `${ms}ms (Poor)`;
  },

  load(percentage) {
    if (percentage < 30) return `${percentage}% (Low)`;
    if (percentage < 70) return `${percentage}% (Medium)`;
    return `${percentage}% (High)`;
  },

  bytes(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
};

// Export utilities for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    Storage,
    Network,
    Theme,
    Notifications,
    Validator,
    Animation,
    debounce,
    Format
  };
}
