{"name": "securevpn-pro", "version": "1.0.0", "description": "Professional VPN Chrome extension with advanced security features", "main": "background.js", "scripts": {"build": "echo 'Building extension...' && npm run validate", "validate": "node scripts/validate.js", "test": "echo 'Running tests...' && npm run test-manifest && npm run test-security", "test-manifest": "node scripts/test-manifest.js", "test-security": "node scripts/test-security.js", "package": "npm run build && node scripts/package.js", "dev": "echo 'Development mode - load extension in Chrome'", "lint": "eslint *.js", "format": "prettier --write *.js *.json *.css *.html"}, "keywords": ["vpn", "proxy", "privacy", "security", "chrome-extension", "webrtc-protection", "dns-protection", "socks5", "http-proxy"], "author": "SecureVPN Pro Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/securevpn-pro.git"}, "bugs": {"url": "https://github.com/your-repo/securevpn-pro/issues"}, "homepage": "https://github.com/your-repo/securevpn-pro#readme", "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0", "archiver": "^5.0.0"}, "engines": {"node": ">=14.0.0"}, "chrome": {"minimum_version": "88"}, "permissions": ["proxy", "storage", "activeTab", "notifications", "webRequest", "webRequestBlocking", "declarativeNetRequest", "declarativeNetRequestWithHostAccess"], "manifest_version": 3}