// SecureVPN Pro - Content Script for WebRTC and DNS Protection

(function() {
  'use strict';

  // Check if already injected
  if (window.secureVPNInjected) {
    return;
  }
  window.secureVPNInjected = true;

  // WebRTC Protection
  function blockWebRTC() {
    // Override RTCPeerConnection
    if (typeof RTCPeerConnection !== 'undefined') {
      const OriginalRTCPeerConnection = RTCPeerConnection;
      
      RTCPeerConnection = function(configuration, constraints) {
        if (configuration && configuration.iceServers) {
          // Remove or modify ICE servers to prevent IP leaks
          configuration.iceServers = configuration.iceServers.filter(server => {
            // Allow only relay servers, block STUN servers
            return server.urls && server.urls.some(url => 
              url.startsWith('turn:') || url.startsWith('turns:')
            );
          });
        }
        
        return new OriginalRTCPeerConnection(configuration, constraints);
      };
      
      // Copy static methods and properties
      Object.setPrototypeOf(RTCPeerConnection, OriginalRTCPeerConnection);
      RTCPeerConnection.prototype = OriginalRTCPeerConnection.prototype;
      
      console.log('[SecureVPN] WebRTC protection enabled');
    }

    // Override webkitRTCPeerConnection for older browsers
    if (typeof webkitRTCPeerConnection !== 'undefined') {
      const OriginalWebkitRTCPeerConnection = webkitRTCPeerConnection;
      
      webkitRTCPeerConnection = function(configuration, constraints) {
        if (configuration && configuration.iceServers) {
          configuration.iceServers = configuration.iceServers.filter(server => {
            return server.urls && server.urls.some(url => 
              url.startsWith('turn:') || url.startsWith('turns:')
            );
          });
        }
        
        return new OriginalWebkitRTCPeerConnection(configuration, constraints);
      };
      
      Object.setPrototypeOf(webkitRTCPeerConnection, OriginalWebkitRTCPeerConnection);
      webkitRTCPeerConnection.prototype = OriginalWebkitRTCPeerConnection.prototype;
    }

    // Block getUserMedia for additional protection
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
      
      navigator.mediaDevices.getUserMedia = function(constraints) {
        console.log('[SecureVPN] getUserMedia request intercepted');
        
        // You can modify this to allow/block based on your needs
        return originalGetUserMedia.call(this, constraints);
      };
    }
  }

  // DNS Protection
  function protectDNS() {
    // Override fetch to redirect DNS queries
    if (typeof fetch !== 'undefined') {
      const originalFetch = fetch;
      
      fetch = function(input, init) {
        const url = typeof input === 'string' ? input : input.url;
        
        // Redirect DNS-over-HTTPS queries to secure servers
        if (url.includes('dns.google') || 
            url.includes('cloudflare-dns.com') ||
            url.includes('/dns-query')) {
          
          console.log('[SecureVPN] DNS query redirected for protection');
          
          // Redirect to Cloudflare's secure DNS
          const secureUrl = url.replace(/https?:\/\/[^\/]+/, 'https://1.1.1.1');
          return originalFetch.call(this, secureUrl, init);
        }
        
        return originalFetch.call(this, input, init);
      };
    }

    // Override XMLHttpRequest for DNS protection
    if (typeof XMLHttpRequest !== 'undefined') {
      const OriginalXMLHttpRequest = XMLHttpRequest;
      
      XMLHttpRequest = function() {
        const xhr = new OriginalXMLHttpRequest();
        const originalOpen = xhr.open;
        
        xhr.open = function(method, url, async, user, password) {
          // Check for DNS queries
          if (typeof url === 'string' && 
              (url.includes('dns.google') || 
               url.includes('cloudflare-dns.com') ||
               url.includes('/dns-query'))) {
            
            console.log('[SecureVPN] DNS XHR request redirected');
            url = url.replace(/https?:\/\/[^\/]+/, 'https://1.1.1.1');
          }
          
          return originalOpen.call(this, method, url, async, user, password);
        };
        
        return xhr;
      };
      
      XMLHttpRequest.prototype = OriginalXMLHttpRequest.prototype;
    }
  }

  // Geolocation Protection
  function protectGeolocation() {
    if (navigator.geolocation) {
      const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
      const originalWatchPosition = navigator.geolocation.watchPosition;
      
      navigator.geolocation.getCurrentPosition = function(success, error, options) {
        console.log('[SecureVPN] Geolocation request intercepted');
        
        // You can choose to block or spoof the location
        if (error) {
          error({
            code: 1,
            message: 'Location access denied by SecureVPN'
          });
        }
      };
      
      navigator.geolocation.watchPosition = function(success, error, options) {
        console.log('[SecureVPN] Geolocation watch request intercepted');
        
        if (error) {
          error({
            code: 1,
            message: 'Location access denied by SecureVPN'
          });
        }
        
        return -1; // Invalid watch ID
      };
    }
  }

  // Canvas Fingerprinting Protection
  function protectCanvas() {
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
    
    HTMLCanvasElement.prototype.toDataURL = function() {
      console.log('[SecureVPN] Canvas fingerprinting attempt blocked');
      
      // Return a generic canvas data URL
      const canvas = document.createElement('canvas');
      canvas.width = this.width;
      canvas.height = this.height;
      return originalToDataURL.call(canvas);
    };
    
    CanvasRenderingContext2D.prototype.getImageData = function() {
      console.log('[SecureVPN] Canvas image data access blocked');
      
      // Return empty image data
      return new ImageData(1, 1);
    };
  }

  // WebGL Fingerprinting Protection
  function protectWebGL() {
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
      console.log('[SecureVPN] WebGL fingerprinting attempt blocked');
      
      // Return generic values for fingerprinting parameters
      switch (parameter) {
        case this.VENDOR:
          return 'Generic Vendor';
        case this.RENDERER:
          return 'Generic Renderer';
        case this.VERSION:
          return 'WebGL 1.0';
        case this.SHADING_LANGUAGE_VERSION:
          return 'WebGL GLSL ES 1.0';
        default:
          return originalGetParameter.call(this, parameter);
      }
    };
  }

  // Battery API Protection
  function protectBattery() {
    if (navigator.getBattery) {
      navigator.getBattery = function() {
        console.log('[SecureVPN] Battery API access blocked');
        return Promise.reject(new Error('Battery API blocked by SecureVPN'));
      };
    }
  }

  // Screen Resolution Protection
  function protectScreen() {
    // Override screen properties
    Object.defineProperty(screen, 'width', {
      get: function() { return 1920; }
    });
    
    Object.defineProperty(screen, 'height', {
      get: function() { return 1080; }
    });
    
    Object.defineProperty(screen, 'availWidth', {
      get: function() { return 1920; }
    });
    
    Object.defineProperty(screen, 'availHeight', {
      get: function() { return 1040; }
    });
  }

  // Timezone Protection
  function protectTimezone() {
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    
    Date.prototype.getTimezoneOffset = function() {
      console.log('[SecureVPN] Timezone fingerprinting blocked');
      return 0; // Return UTC timezone
    };
  }

  // Check if VPN is connected and apply protections
  function checkVPNStatus() {
    chrome.runtime.sendMessage({ action: 'getStatus' }, (response) => {
      if (response && response.connected) {
        console.log('[SecureVPN] Applying privacy protections');
        
        // Apply all protections when VPN is connected
        blockWebRTC();
        protectDNS();
        protectGeolocation();
        protectCanvas();
        protectWebGL();
        protectBattery();
        protectScreen();
        protectTimezone();
      }
    });
  }

  // Initialize protections
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkVPNStatus);
  } else {
    checkVPNStatus();
  }

  // Listen for VPN status changes
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'vpnStatusChanged') {
      if (message.connected) {
        console.log('[SecureVPN] VPN connected - enabling protections');
        checkVPNStatus();
      } else {
        console.log('[SecureVPN] VPN disconnected');
      }
    }
  });

  console.log('[SecureVPN] Content script loaded');
})();
