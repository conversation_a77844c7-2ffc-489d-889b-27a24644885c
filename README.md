# SecureVPN Pro - Chrome Extension

A professional VPN extension for Google Chrome with advanced security features, beautiful UI, and high-performance proxy protocols.

## 🚀 Features

### 🔒 Security & Privacy
- **WebRTC Leak Protection**: Prevents IP address leaks through WebRTC
- **DNS Leak Protection**: Redirects DNS queries through secure servers
- **Canvas Fingerprinting Protection**: Blocks canvas-based tracking
- **WebGL Fingerprinting Protection**: Prevents WebGL-based fingerprinting
- **Geolocation Protection**: Blocks location tracking attempts
- **Battery API Protection**: Prevents battery status fingerprinting

### 🌐 VPN Capabilities
- **SOCKS5 Proxy Support**: High-performance SOCKS5 protocol
- **HTTP Proxy Support**: Standard HTTP proxy protocol
- **Multiple Server Locations**: US, UK, Germany, Japan, Singapore
- **Auto-Connect**: Automatically connect on browser startup
- **Server Load Monitoring**: Real-time server load and ping display
- **Seamless Server Switching**: Switch between servers instantly

### 🎨 User Interface
- **Beautiful Modern Design**: Clean and intuitive interface
- **Dark Mode Support**: Toggle between light and dark themes
- **Real-time Status**: Live connection status and server information
- **Server Statistics**: Ping times and server load indicators
- **Responsive Design**: Optimized for all screen sizes

### ⚡ Performance
- **Fast Connection**: Optimized proxy configuration
- **Low Latency**: Minimal impact on browsing speed
- **Reliable**: Auto-reconnection on network changes
- **Efficient**: Minimal resource usage

## 📦 Installation

### Method 1: Developer Mode (Recommended for Testing)

1. **Download the Extension**
   ```bash
   git clone https://github.com/your-repo/securevpn-pro.git
   cd securevpn-pro
   ```

2. **Open Chrome Extensions Page**
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right corner)

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the extension folder
   - The extension will appear in your extensions list

4. **Pin the Extension**
   - Click the puzzle piece icon in Chrome toolbar
   - Find "SecureVPN Pro" and click the pin icon

### Method 2: Chrome Web Store (Coming Soon)
The extension will be available on the Chrome Web Store after review.

## 🔧 Configuration

### Server Configuration
Edit `servers.json` to add your own VPN servers:

```json
{
  "servers": [
    {
      "id": "custom-server-1",
      "name": "Custom Server",
      "country": "US",
      "city": "New York",
      "flag": "🇺🇸",
      "host": "your-server.com",
      "port": 1080,
      "type": "socks5",
      "username": "your-username",
      "password": "your-password",
      "ping": 45,
      "load": 23
    }
  ]
}
```

### Supported Proxy Types
- **SOCKS5**: `"type": "socks5"`
- **HTTP**: `"type": "http"`

## 🎯 Usage

### Basic Usage

1. **Select a Server**
   - Click the SecureVPN Pro icon
   - Choose a server from the list
   - Servers show ping time and load percentage

2. **Connect**
   - Click the "Connect" button
   - Wait for connection confirmation
   - Green indicator shows successful connection

3. **Disconnect**
   - Click "Disconnect" when connected
   - Red indicator shows disconnected state

### Advanced Settings

- **Auto-Connect**: Automatically connect on browser startup
- **WebRTC Protection**: Block WebRTC IP leaks
- **DNS Protection**: Secure DNS queries

### Keyboard Shortcuts
- `Ctrl+Shift+V`: Toggle VPN connection (coming soon)

## 🛠️ Development

### Project Structure
```
securevpn-pro/
├── manifest.json          # Extension manifest (V3)
├── background.js          # Service worker
├── popup.html            # Extension popup UI
├── popup.js              # Popup logic
├── content.js            # Content script for protection
├── utils.js              # Utility functions
├── styles.css            # UI styles
├── servers.json          # Server configuration
├── rules.json            # Security rules
├── icons/                # Extension icons
└── README.md             # This file
```

### Building from Source

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-repo/securevpn-pro.git
   cd securevpn-pro
   ```

2. **Install Dependencies** (if any)
   ```bash
   npm install  # If using build tools
   ```

3. **Load in Chrome**
   - Follow installation instructions above

### Testing

1. **Test Connection**
   - Connect to a server
   - Visit https://whatismyipaddress.com/
   - Verify IP address change

2. **Test WebRTC Protection**
   - Visit https://browserleaks.com/webrtc
   - Verify no IP leaks when connected

3. **Test DNS Protection**
   - Check DNS queries are redirected
   - Monitor network tab in DevTools

## 🔐 Security Features Explained

### WebRTC Leak Protection
WebRTC can expose your real IP address even when using a VPN. Our extension:
- Overrides RTCPeerConnection API
- Filters ICE servers to prevent STUN requests
- Blocks WebRTC-related network requests

### DNS Leak Protection
DNS queries can reveal your browsing activity. Our protection:
- Redirects DNS-over-HTTPS requests to secure servers
- Uses Cloudflare's ******* DNS service
- Blocks insecure DNS requests

### Fingerprinting Protection
Prevents websites from tracking you through:
- Canvas fingerprinting
- WebGL fingerprinting
- Screen resolution detection
- Battery status API
- Timezone detection

## 📱 Browser Compatibility

- ✅ Google Chrome (88+)
- ✅ Microsoft Edge (88+)
- ✅ Brave Browser
- ✅ Opera (74+)
- ❌ Firefox (different extension format)

## 🐛 Troubleshooting

### Common Issues

**Connection Failed**
- Check server credentials
- Verify internet connection
- Try different server

**Slow Browsing**
- Switch to closer server
- Check server load percentage
- Disable unnecessary protections

**Extension Not Loading**
- Reload extension in chrome://extensions/
- Check for JavaScript errors in console
- Verify manifest.json syntax

### Debug Mode
Enable debug logging:
1. Open DevTools (F12)
2. Go to Console tab
3. Look for SecureVPN messages

## 📄 Privacy Policy

This extension:
- Does NOT log your browsing activity
- Does NOT store personal information
- Only stores connection preferences locally
- Uses secure proxy protocols

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📞 Support

- **Email**: <EMAIL>
- **GitHub Issues**: [Create an issue](https://github.com/your-repo/securevpn-pro/issues)
- **Documentation**: [Wiki](https://github.com/your-repo/securevpn-pro/wiki)

## 📜 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Chrome Extension APIs
- Cloudflare DNS Service
- Modern CSS Design Patterns
- WebRTC Security Research

---

**⚠️ Disclaimer**: This extension is for educational and privacy purposes. Ensure compliance with local laws and terms of service when using VPN services.
