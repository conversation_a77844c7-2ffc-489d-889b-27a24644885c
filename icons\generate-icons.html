<!DOCTYPE html>
<html>
<head>
    <title>Generate Icons</title>
</head>
<body>
    <h2>Generating Icons...</h2>
    <canvas id="canvas" style="display: none;"></canvas>
    
    <script>
        const svgContent = `<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
          <circle cx="64" cy="64" r="60" fill="#4f46e5" stroke="#3730a3" stroke-width="4"/>
          <path d="M64 20 L88 32 L88 56 C88 72 80 84 64 100 C48 84 40 72 40 56 L40 32 Z" 
                fill="white" stroke="#4f46e5" stroke-width="2"/>
          <text x="64" y="50" text-anchor="middle" font-family="Arial, sans-serif" 
                font-size="12" font-weight="bold" fill="#4f46e5">VPN</text>
          <rect x="58" y="65" width="12" height="8" fill="#4f46e5" rx="1"/>
          <path d="M60 65 L60 62 C60 60 62 58 64 58 C66 58 68 60 68 62 L68 65" 
                fill="none" stroke="#4f46e5" stroke-width="2"/>
        </svg>`;

        function generateIcon(size, filename) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            img.src = 'data:image/svg+xml;base64,' + btoa(svgContent);
        }

        // Generate all required icon sizes
        setTimeout(() => {
            generateIcon(16, 'icon16.png');
        }, 100);
        
        setTimeout(() => {
            generateIcon(32, 'icon32.png');
        }, 200);
        
        setTimeout(() => {
            generateIcon(48, 'icon48.png');
        }, 300);
        
        setTimeout(() => {
            generateIcon(128, 'icon128.png');
        }, 400);

        document.body.innerHTML += '<p>Icons generated! Check your downloads folder and move them to the icons/ directory.</p>';
    </script>
</body>
</html>
