<!DOCTYPE html>
<html>
<head>
    <title>Create PNG Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { margin: 10px; display: inline-block; }
        canvas { border: 1px solid #ccc; margin: 5px; }
        button { background: #4f46e5; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>SecureVPN Pro - Icon Generator</h1>
    <div class="status" id="status">Click "Generate All Icons" to create the required PNG files</div>
    
    <button onclick="generateAllIcons()">Generate All Icons</button>
    <button onclick="downloadAllIcons()">Download All Icons</button>
    
    <div id="previews"></div>
    
    <script>
        const iconSizes = [16, 32, 48, 128];
        const canvases = {};
        
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Clear background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, size, size);
            
            // Draw background circle
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.4;
            
            ctx.fillStyle = '#4f46e5';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw shield shape
            const shieldWidth = size * 0.3;
            const shieldHeight = size * 0.35;
            const shieldX = centerX - shieldWidth / 2;
            const shieldY = centerY - shieldHeight / 2;
            
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.moveTo(centerX, shieldY);
            ctx.lineTo(shieldX + shieldWidth, shieldY + shieldHeight * 0.3);
            ctx.lineTo(shieldX + shieldWidth, shieldY + shieldHeight * 0.7);
            ctx.lineTo(centerX, shieldY + shieldHeight);
            ctx.lineTo(shieldX, shieldY + shieldHeight * 0.7);
            ctx.lineTo(shieldX, shieldY + shieldHeight * 0.3);
            ctx.closePath();
            ctx.fill();
            
            // Draw VPN text (only for larger sizes)
            if (size >= 32) {
                ctx.fillStyle = '#4f46e5';
                ctx.font = `bold ${size * 0.12}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('VPN', centerX, centerY - size * 0.05);
            }
            
            // Draw lock icon (only for larger sizes)
            if (size >= 48) {
                const lockSize = size * 0.08;
                const lockX = centerX - lockSize / 2;
                const lockY = centerY + size * 0.08;
                
                ctx.fillStyle = '#4f46e5';
                ctx.fillRect(lockX, lockY, lockSize, lockSize * 0.8);
                
                ctx.strokeStyle = '#4f46e5';
                ctx.lineWidth = size * 0.02;
                ctx.beginPath();
                ctx.arc(centerX, lockY, lockSize * 0.3, Math.PI, 0);
                ctx.stroke();
            }
            
            return canvas;
        }
        
        function generateAllIcons() {
            const previewsDiv = document.getElementById('previews');
            previewsDiv.innerHTML = '';
            
            iconSizes.forEach(size => {
                const canvas = createIcon(size);
                canvases[size] = canvas;
                
                const div = document.createElement('div');
                div.className = 'icon-preview';
                div.innerHTML = `<h3>${size}x${size}</h3>`;
                div.appendChild(canvas);
                previewsDiv.appendChild(div);
            });
            
            document.getElementById('status').innerHTML = 'Icons generated! Click "Download All Icons" to save them.';
        }
        
        function downloadIcon(size) {
            const canvas = canvases[size];
            if (!canvas) return;
            
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }
        
        function downloadAllIcons() {
            if (Object.keys(canvases).length === 0) {
                alert('Please generate icons first!');
                return;
            }
            
            iconSizes.forEach((size, index) => {
                setTimeout(() => {
                    downloadIcon(size);
                }, index * 500); // Delay downloads to avoid browser blocking
            });
            
            document.getElementById('status').innerHTML = 'All icons downloaded! Move them to the F:\\Users\\VPN\\icons\\ folder and reload the extension.';
        }
        
        // Auto-generate on load
        window.onload = function() {
            generateAllIcons();
        };
    </script>
</body>
</html>
