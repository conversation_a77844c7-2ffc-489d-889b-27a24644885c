<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureVPN Pro - Advanced Settings</title>
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
        }

        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.125rem;
            color: var(--text-secondary);
        }

        .section {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .setting-group {
            margin-bottom: 2rem;
        }

        .setting-group:last-child {
            margin-bottom: 0;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            margin-bottom: 1rem;
        }

        .setting-item:last-child {
            margin-bottom: 0;
        }

        .setting-info {
            flex: 1;
        }

        .setting-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .setting-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .setting-control {
            margin-left: 1rem;
        }

        .toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-tertiary);
            transition: 0.3s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-group label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .input-group input,
        .input-group select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 0.5rem;
        }

        .button:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .button.secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .button.secondary:hover {
            background: var(--border-color);
        }

        .button.danger {
            background: var(--danger-color);
        }

        .button.danger:hover {
            background: #dc2626;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-card {
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .theme-toggle {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--bg-tertiary);
        }

        .theme-icon {
            width: 20px;
            height: 20px;
            fill: var(--text-primary);
        }

        .alert {
            padding: 1rem;
            border-radius: var(--radius-md);
            margin-bottom: 1rem;
            border-left: 4px solid;
        }

        .alert.info {
            background: rgba(79, 70, 229, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .alert.warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .alert.success {
            background: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: var(--success-color);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="themeToggle">
        <svg class="theme-icon" viewBox="0 0 24 24">
            <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
        </svg>
    </button>

    <div class="container">
        <div class="header">
            <h1>SecureVPN Pro</h1>
            <p>Advanced Settings & Configuration</p>
        </div>

        <!-- Connection Settings -->
        <div class="section">
            <h2>🔗 Connection Settings</h2>
            <p>Configure how the VPN connects and behaves</p>
            
            <div class="setting-group">
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Auto-connect on startup</div>
                        <div class="setting-description">Automatically connect to VPN when Chrome starts</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="autoConnect">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Auto-reconnect</div>
                        <div class="setting-description">Automatically reconnect if connection is lost</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="autoReconnect" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Connection timeout</div>
                        <div class="setting-description">How long to wait before timing out (seconds)</div>
                    </div>
                    <div class="setting-control">
                        <div class="input-group">
                            <input type="number" id="connectionTimeout" value="30" min="10" max="120">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="section">
            <h2>🛡️ Security & Privacy</h2>
            <p>Advanced security features to protect your privacy</p>
            
            <div class="setting-group">
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Block WebRTC leaks</div>
                        <div class="setting-description">Prevent IP address leaks through WebRTC</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="blockWebRTC" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">DNS leak protection</div>
                        <div class="setting-description">Route DNS queries through secure servers</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="dnsProtection" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Block fingerprinting</div>
                        <div class="setting-description">Prevent canvas and WebGL fingerprinting</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="blockFingerprinting" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Secure DNS server</div>
                        <div class="setting-description">Choose your preferred secure DNS provider</div>
                    </div>
                    <div class="setting-control">
                        <div class="input-group">
                            <select id="dnsServer">
                                <option value="*******">Cloudflare (*******)</option>
                                <option value="*******">Google (*******)</option>
                                <option value="*******">Quad9 (*******)</option>
                                <option value="**************">OpenDNS</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications -->
        <div class="section">
            <h2>🔔 Notifications</h2>
            <p>Control when and how you receive notifications</p>
            
            <div class="setting-group">
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Connection notifications</div>
                        <div class="setting-description">Show notifications when connecting/disconnecting</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="connectionNotifications" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Error notifications</div>
                        <div class="setting-description">Show notifications for connection errors</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="errorNotifications" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="section">
            <h2>📊 Statistics</h2>
            <p>View your VPN usage statistics</p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalConnections">0</div>
                    <div class="stat-label">Total Connections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalTime">0h 0m</div>
                    <div class="stat-label">Total Time Connected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="dataTransferred">0 MB</div>
                    <div class="stat-label">Data Transferred</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="favoriteServer">-</div>
                    <div class="stat-label">Most Used Server</div>
                </div>
            </div>
        </div>

        <!-- Advanced -->
        <div class="section">
            <h2>⚙️ Advanced</h2>
            <p>Advanced configuration options</p>
            
            <div class="alert info">
                <strong>Note:</strong> These settings are for advanced users only. Changing them may affect the extension's functionality.
            </div>

            <div class="setting-group">
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Debug logging</div>
                        <div class="setting-description">Enable detailed logging for troubleshooting</div>
                    </div>
                    <div class="setting-control">
                        <label class="toggle">
                            <input type="checkbox" id="debugLogging">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">Proxy bypass list</div>
                        <div class="setting-description">Domains that should bypass the VPN (one per line)</div>
                    </div>
                    <div class="setting-control">
                        <div class="input-group">
                            <textarea id="bypassList" rows="4" style="resize: vertical; font-family: monospace;">localhost
127.0.0.1
*.local
10.*
192.168.*</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 2rem;">
                <button class="button" onclick="saveSettings()">Save Settings</button>
                <button class="button secondary" onclick="resetSettings()">Reset to Defaults</button>
                <button class="button danger" onclick="clearData()">Clear All Data</button>
            </div>
        </div>
    </div>

    <script>
        // Theme management
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);

        // Settings management
        async function loadSettings() {
            try {
                const settings = await chrome.storage.local.get([
                    'autoConnect', 'autoReconnect', 'connectionTimeout',
                    'blockWebRTC', 'dnsProtection', 'blockFingerprinting', 'dnsServer',
                    'connectionNotifications', 'errorNotifications',
                    'debugLogging', 'bypassList'
                ]);

                // Apply settings to UI
                Object.keys(settings).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = settings[key] || false;
                        } else {
                            element.value = settings[key] || element.value;
                        }
                    }
                });

                // Load statistics
                loadStatistics();
            } catch (error) {
                console.error('Failed to load settings:', error);
            }
        }

        async function saveSettings() {
            try {
                const settings = {};
                const inputs = document.querySelectorAll('input, select, textarea');
                
                inputs.forEach(input => {
                    if (input.id) {
                        if (input.type === 'checkbox') {
                            settings[input.id] = input.checked;
                        } else {
                            settings[input.id] = input.value;
                        }
                    }
                });

                await chrome.storage.local.set(settings);
                
                // Show success message
                showAlert('Settings saved successfully!', 'success');
                
                // Notify background script of changes
                chrome.runtime.sendMessage({ action: 'settingsChanged', settings });
            } catch (error) {
                console.error('Failed to save settings:', error);
                showAlert('Failed to save settings', 'warning');
            }
        }

        async function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                try {
                    await chrome.storage.local.clear();
                    location.reload();
                } catch (error) {
                    console.error('Failed to reset settings:', error);
                }
            }
        }

        async function clearData() {
            if (confirm('This will clear all data including statistics. Are you sure?')) {
                try {
                    await chrome.storage.local.clear();
                    showAlert('All data cleared successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } catch (error) {
                    console.error('Failed to clear data:', error);
                }
            }
        }

        async function loadStatistics() {
            try {
                const stats = await chrome.storage.local.get([
                    'totalConnections', 'totalTime', 'dataTransferred', 'favoriteServer'
                ]);

                document.getElementById('totalConnections').textContent = stats.totalConnections || 0;
                document.getElementById('totalTime').textContent = formatTime(stats.totalTime || 0);
                document.getElementById('dataTransferred').textContent = formatBytes(stats.dataTransferred || 0);
                document.getElementById('favoriteServer').textContent = stats.favoriteServer || '-';
            } catch (error) {
                console.error('Failed to load statistics:', error);
            }
        }

        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '1000';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', loadSettings);
    </script>
</body>
</html>
