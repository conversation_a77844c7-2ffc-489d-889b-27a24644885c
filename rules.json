[{"id": 1, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*stun*", "resourceTypes": ["xmlhttprequest", "other"]}}, {"id": 2, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*turn*", "resourceTypes": ["xmlhttprequest", "other"]}}, {"id": 3, "priority": 1, "action": {"type": "redirect", "redirect": {"url": "https://*******/dns-query"}}, "condition": {"urlFilter": "*dns.google*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 4, "priority": 1, "action": {"type": "redirect", "redirect": {"url": "https://*******/dns-query"}}, "condition": {"urlFilter": "*cloudflare-dns.com*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 5, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*webrtc*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 6, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*fingerprint*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 7, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*tracking*", "resourceTypes": ["script", "xmlhttprequest", "image"]}}, {"id": 8, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*analytics*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 9, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*doubleclick*", "resourceTypes": ["script", "xmlhttprequest", "image"]}}, {"id": 10, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*googlesyndication*", "resourceTypes": ["script", "xmlhttprequest"]}}]