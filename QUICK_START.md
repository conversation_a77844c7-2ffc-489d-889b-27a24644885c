# 🚀 دليل البدء السريع - SecureVPN Pro

## ⚡ التثبيت السريع (5 دقائق)

### 1. تحميل الإضافة
```bash
# استنساخ المستودع
git clone https://github.com/your-repo/securevpn-pro.git
cd securevpn-pro

# أو تحميل ZIP وفك الضغط
```

### 2. تثبيت في Chrome
1. افتح `chrome://extensions/`
2. فعّل "Developer mode" 
3. اضغط "Load unpacked"
4. اختر مجلد الإضافة
5. ثبّت الأيقونة في شريط الأدوات

### 3. الاستخدام الفوري
1. اضغط على أيقونة SecureVPN Pro
2. اختر خادم (مثل: United States)
3. اضغط "Connect"
4. انتظر النقطة الخضراء ✅

## 🎯 الاستخدام الأساسي

### اتصال سريع
```
1. اضغط الأيقونة → 2. اختر خادم → 3. Connect → ✅ متصل!
```

### تبديل الخوادم
```
أثناء الاتصال → اختر خادم جديد → يتبدل تلقائياً
```

### قطع الاتصال
```
اضغط "Disconnect" → ❌ منقطع
```

## ⚙️ الإعدادات المهمة

### تفعيل الحماية الكاملة
```
☑️ Block WebRTC leaks      (منع تسرب IP)
☑️ DNS leak protection    (حماية DNS)
☐ Auto-connect on startup (اتصال تلقائي)
```

### اختيار أفضل خادم
- **Ping < 100ms** = سرعة جيدة
- **Load < 50%** = أداء مثالي
- **الموقع الأقرب** = أسرع اتصال

## 🔧 إضافة خوادم مخصصة

### تحرير servers.json
```json
{
  "id": "my-server",
  "name": "خادمي الخاص",
  "host": "my-vpn.com",
  "port": 1080,
  "type": "socks5",
  "username": "user",
  "password": "pass"
}
```

### أنواع البروكسي
- **SOCKS5**: `"type": "socks5"` (الأفضل)
- **HTTP**: `"type": "http"` (عادي)

## ✅ اختبار الاتصال

### تحقق من IP الجديد
```
زر: https://whatismyipaddress.com/
النتيجة: IP مختلف + موقع جديد = ✅ يعمل
```

### اختبار تسرب WebRTC
```
زر: https://browserleaks.com/webrtc
النتيجة: لا توجد تسريبات = ✅ محمي
```

### اختبار DNS
```
زر: https://dnsleaktest.com/
النتيجة: DNS آمن = ✅ محمي
```

## 🎨 الميزات الجمالية

### الوضع الليلي
```
اضغط أيقونة الشمس/القمر → يتبدل فوراً
```

### المؤشرات البصرية
- 🔴 **أحمر**: منقطع
- 🟡 **أصفر**: يتصل...
- 🟢 **أخضر**: متصل

### معلومات الخادم
- **العلم**: البلد
- **Ping**: سرعة الاستجابة
- **Load**: حمولة الخادم

## 🛠️ حل المشاكل السريع

### فشل الاتصال
```
1. جرب خادم آخر
2. تحقق من الإنترنت
3. أعد تحميل الإضافة
```

### بطء في التصفح
```
1. اختر خادم أقرب
2. تحقق من Load%
3. جرب SOCKS5 بدلاً من HTTP
```

### الإضافة لا تظهر
```
1. chrome://extensions/
2. اضغط 🔄 بجانب الإضافة
3. تحقق من Developer mode
```

## 📱 اختصارات مفيدة

### صفحات مفيدة
- **الإضافات**: `chrome://extensions/`
- **إعدادات البروكسي**: `chrome://settings/`
- **أدوات المطور**: `F12`

### اختبارات سريعة
- **IP الحالي**: `curl ifconfig.me`
- **DNS الحالي**: `nslookup google.com`
- **سرعة الإنترنت**: `speedtest.net`

## 🔐 نصائح الأمان

### ✅ افعل
- استخدم خوادم موثوقة
- فعّل جميع ميزات الحماية
- تحقق من IP بانتظام
- استخدم HTTPS دائماً

### ❌ لا تفعل
- تدخل كلمات مرور على HTTP
- تثق في خوادم مجانية مشكوك فيها
- تترك الاتصال مفتوح بلا داع
- تتجاهل تحذيرات الأمان

## 📞 مساعدة سريعة

### مشكلة؟ جرب هذا:
1. **أعد تحميل الإضافة**
2. **جرب خادم آخر**
3. **تحقق من Console (F12)**
4. **اقرأ رسالة الخطأ**

### تحتاج مساعدة؟
- **GitHub**: [إنشاء issue](https://github.com/your-repo/securevpn-pro/issues)
- **البريد**: <EMAIL>
- **الوثائق**: [README.md](README.md)

---

## 🎉 مبروك!

أنت الآن جاهز لاستخدام SecureVPN Pro بأمان وخصوصية كاملة!

**تذكر**: 
- ✅ تحقق من IP بعد الاتصال
- ✅ فعّل جميع ميزات الحماية  
- ✅ استخدم خوادم موثوقة فقط

**استمتع بتصفح آمن! 🛡️**
