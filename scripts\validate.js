#!/usr/bin/env node

// Validation script for SecureVPN Pro extension

const fs = require('fs');
const path = require('path');

class ExtensionValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '✓',
      'warning': '⚠',
      'error': '✗'
    }[type];
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  validateManifest() {
    this.log('Validating manifest.json...');
    
    try {
      const manifestPath = path.join(__dirname, '..', 'manifest.json');
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      
      // Check required fields
      const requiredFields = ['manifest_version', 'name', 'version', 'description'];
      for (const field of requiredFields) {
        if (!manifest[field]) {
          this.errors.push(`Missing required field: ${field}`);
        }
      }
      
      // Check manifest version
      if (manifest.manifest_version !== 3) {
        this.errors.push('Manifest version must be 3');
      }
      
      // Check permissions
      if (!manifest.permissions || !Array.isArray(manifest.permissions)) {
        this.errors.push('Permissions must be an array');
      }
      
      // Check background script
      if (!manifest.background || !manifest.background.service_worker) {
        this.errors.push('Background service worker is required');
      }
      
      // Check action
      if (!manifest.action || !manifest.action.default_popup) {
        this.errors.push('Action with default popup is required');
      }
      
      this.log('Manifest validation completed');
      
    } catch (error) {
      this.errors.push(`Failed to parse manifest.json: ${error.message}`);
    }
  }

  validateFiles() {
    this.log('Validating required files...');
    
    const requiredFiles = [
      'manifest.json',
      'background.js',
      'popup.html',
      'popup.js',
      'content.js',
      'utils.js',
      'styles.css',
      'servers.json',
      'rules.json'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(__dirname, '..', file);
      if (!fs.existsSync(filePath)) {
        this.errors.push(`Missing required file: ${file}`);
      } else {
        this.log(`Found: ${file}`);
      }
    }
  }

  validateServers() {
    this.log('Validating servers.json...');
    
    try {
      const serversPath = path.join(__dirname, '..', 'servers.json');
      const servers = JSON.parse(fs.readFileSync(serversPath, 'utf8'));
      
      if (!servers.servers || !Array.isArray(servers.servers)) {
        this.errors.push('servers.json must contain a servers array');
        return;
      }
      
      for (const server of servers.servers) {
        const requiredFields = ['id', 'name', 'host', 'port', 'type', 'username', 'password'];
        for (const field of requiredFields) {
          if (!server[field]) {
            this.errors.push(`Server ${server.id || 'unknown'} missing field: ${field}`);
          }
        }
        
        // Validate port
        if (server.port && (server.port < 1 || server.port > 65535)) {
          this.errors.push(`Server ${server.id} has invalid port: ${server.port}`);
        }
        
        // Validate type
        if (server.type && !['socks5', 'http'].includes(server.type)) {
          this.errors.push(`Server ${server.id} has invalid type: ${server.type}`);
        }
      }
      
      this.log(`Validated ${servers.servers.length} servers`);
      
    } catch (error) {
      this.errors.push(`Failed to parse servers.json: ${error.message}`);
    }
  }

  validateRules() {
    this.log('Validating rules.json...');
    
    try {
      const rulesPath = path.join(__dirname, '..', 'rules.json');
      const rules = JSON.parse(fs.readFileSync(rulesPath, 'utf8'));
      
      if (!Array.isArray(rules)) {
        this.errors.push('rules.json must be an array');
        return;
      }
      
      for (const rule of rules) {
        if (!rule.id || !rule.action || !rule.condition) {
          this.errors.push(`Rule missing required fields: id, action, condition`);
        }
        
        if (rule.id && typeof rule.id !== 'number') {
          this.errors.push(`Rule ID must be a number: ${rule.id}`);
        }
      }
      
      this.log(`Validated ${rules.length} rules`);
      
    } catch (error) {
      this.errors.push(`Failed to parse rules.json: ${error.message}`);
    }
  }

  validateCSS() {
    this.log('Validating CSS...');
    
    try {
      const cssPath = path.join(__dirname, '..', 'styles.css');
      const css = fs.readFileSync(cssPath, 'utf8');
      
      // Basic CSS validation
      const openBraces = (css.match(/{/g) || []).length;
      const closeBraces = (css.match(/}/g) || []).length;
      
      if (openBraces !== closeBraces) {
        this.errors.push('CSS has mismatched braces');
      }
      
      // Check for CSS variables
      if (!css.includes(':root')) {
        this.warnings.push('CSS does not define root variables');
      }
      
      this.log('CSS validation completed');
      
    } catch (error) {
      this.errors.push(`Failed to validate CSS: ${error.message}`);
    }
  }

  validateHTML() {
    this.log('Validating HTML...');
    
    try {
      const htmlPath = path.join(__dirname, '..', 'popup.html');
      const html = fs.readFileSync(htmlPath, 'utf8');
      
      // Basic HTML validation
      if (!html.includes('<!DOCTYPE html>')) {
        this.warnings.push('HTML missing DOCTYPE declaration');
      }
      
      if (!html.includes('<meta charset="UTF-8">')) {
        this.warnings.push('HTML missing charset meta tag');
      }
      
      // Check for required elements
      const requiredIds = ['connectBtn', 'statusIndicator', 'serverList'];
      for (const id of requiredIds) {
        if (!html.includes(`id="${id}"`)) {
          this.errors.push(`HTML missing required element: ${id}`);
        }
      }
      
      this.log('HTML validation completed');
      
    } catch (error) {
      this.errors.push(`Failed to validate HTML: ${error.message}`);
    }
  }

  validateJavaScript() {
    this.log('Validating JavaScript files...');
    
    const jsFiles = ['background.js', 'popup.js', 'content.js', 'utils.js'];
    
    for (const file of jsFiles) {
      try {
        const jsPath = path.join(__dirname, '..', file);
        const js = fs.readFileSync(jsPath, 'utf8');
        
        // Basic syntax check
        try {
          new Function(js);
        } catch (syntaxError) {
          this.errors.push(`Syntax error in ${file}: ${syntaxError.message}`);
        }
        
        // Check for console.log in production
        if (js.includes('console.log') && process.env.NODE_ENV === 'production') {
          this.warnings.push(`${file} contains console.log statements`);
        }
        
        this.log(`Validated: ${file}`);
        
      } catch (error) {
        this.errors.push(`Failed to validate ${file}: ${error.message}`);
      }
    }
  }

  run() {
    this.log('Starting extension validation...');
    
    this.validateManifest();
    this.validateFiles();
    this.validateServers();
    this.validateRules();
    this.validateCSS();
    this.validateHTML();
    this.validateJavaScript();
    
    // Report results
    this.log('\n=== Validation Results ===');
    
    if (this.errors.length > 0) {
      this.log(`Found ${this.errors.length} errors:`, 'error');
      this.errors.forEach(error => this.log(error, 'error'));
    }
    
    if (this.warnings.length > 0) {
      this.log(`Found ${this.warnings.length} warnings:`, 'warning');
      this.warnings.forEach(warning => this.log(warning, 'warning'));
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      this.log('All validations passed! ✨');
    }
    
    return this.errors.length === 0;
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ExtensionValidator();
  const success = validator.run();
  process.exit(success ? 0 : 1);
}

module.exports = ExtensionValidator;
