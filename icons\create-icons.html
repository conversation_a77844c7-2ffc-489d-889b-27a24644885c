<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureVPN Pro - Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .icon-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon {
            margin: 10px;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4338ca;
        }
    </style>
</head>
<body>
    <h1>SecureVPN Pro - Icon Generator</h1>
    <p>Click the buttons below to download the icons in different sizes:</p>
    
    <div class="icon-container">
        <div class="icon-box">
            <h3>Disconnected State</h3>
            <svg class="icon" width="128" height="128" viewBox="0 0 128 128" id="icon-disconnected">
                <!-- Shield outline -->
                <path d="M64 8 L96 24 L96 56 C96 80 80 96 64 120 C48 96 32 80 32 56 L32 24 Z" 
                      fill="none" stroke="#ef4444" stroke-width="4"/>
                
                <!-- VPN text -->
                <text x="64" y="45" text-anchor="middle" font-family="Arial, sans-serif" 
                      font-size="14" font-weight="bold" fill="#ef4444">VPN</text>
                
                <!-- Disconnected indicator -->
                <circle cx="64" cy="65" r="8" fill="#ef4444"/>
                <path d="M56 57 L72 73 M72 57 L56 73" stroke="white" stroke-width="2"/>
            </svg>
            
            <div>
                <button onclick="downloadIcon('icon-disconnected', 16, 'icon16.png')">16x16</button>
                <button onclick="downloadIcon('icon-disconnected', 32, 'icon32.png')">32x32</button>
                <button onclick="downloadIcon('icon-disconnected', 48, 'icon48.png')">48x48</button>
                <button onclick="downloadIcon('icon-disconnected', 128, 'icon128.png')">128x128</button>
            </div>
        </div>
        
        <div class="icon-box">
            <h3>Connected State</h3>
            <svg class="icon" width="128" height="128" viewBox="0 0 128 128" id="icon-connected">
                <!-- Shield filled -->
                <path d="M64 8 L96 24 L96 56 C96 80 80 96 64 120 C48 96 32 80 32 56 L32 24 Z" 
                      fill="#10b981" stroke="#059669" stroke-width="2"/>
                
                <!-- VPN text -->
                <text x="64" y="45" text-anchor="middle" font-family="Arial, sans-serif" 
                      font-size="14" font-weight="bold" fill="white">VPN</text>
                
                <!-- Connected checkmark -->
                <circle cx="64" cy="65" r="8" fill="white"/>
                <path d="M58 65 L62 69 L70 61" stroke="#10b981" stroke-width="2" fill="none"/>
            </svg>
            
            <div>
                <button onclick="downloadIcon('icon-connected', 16, 'icon-connected16.png')">16x16</button>
                <button onclick="downloadIcon('icon-connected', 32, 'icon-connected32.png')">32x32</button>
                <button onclick="downloadIcon('icon-connected', 48, 'icon-connected48.png')">48x48</button>
                <button onclick="downloadIcon('icon-connected', 128, 'icon-connected128.png')">128x128</button>
            </div>
        </div>
        
        <div class="icon-box">
            <h3>Connecting State</h3>
            <svg class="icon" width="128" height="128" viewBox="0 0 128 128" id="icon-connecting">
                <!-- Shield outline -->
                <path d="M64 8 L96 24 L96 56 C96 80 80 96 64 120 C48 96 32 80 32 56 L32 24 Z" 
                      fill="none" stroke="#f59e0b" stroke-width="4"/>
                
                <!-- VPN text -->
                <text x="64" y="45" text-anchor="middle" font-family="Arial, sans-serif" 
                      font-size="14" font-weight="bold" fill="#f59e0b">VPN</text>
                
                <!-- Connecting spinner -->
                <circle cx="64" cy="65" r="8" fill="none" stroke="#f59e0b" stroke-width="2">
                    <animate attributeName="stroke-dasharray" values="0 50;25 25;0 50" dur="1s" repeatCount="indefinite"/>
                    <animateTransform attributeName="transform" type="rotate" values="0 64 65;360 64 65" dur="1s" repeatCount="indefinite"/>
                </circle>
            </svg>
            
            <div>
                <button onclick="downloadIcon('icon-connecting', 16, 'icon-connecting16.png')">16x16</button>
                <button onclick="downloadIcon('icon-connecting', 32, 'icon-connecting32.png')">32x32</button>
                <button onclick="downloadIcon('icon-connecting', 48, 'icon-connecting48.png')">48x48</button>
                <button onclick="downloadIcon('icon-connecting', 128, 'icon-connecting128.png')">128x128</button>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
        <h3>Instructions:</h3>
        <ol>
            <li>Click the buttons above to download icons in different sizes</li>
            <li>Save the downloaded PNG files to the <code>icons/</code> folder</li>
            <li>Make sure the file names match exactly what's specified in manifest.json</li>
            <li>The extension will automatically use the appropriate icon based on connection status</li>
        </ol>
        
        <h3>Required Files:</h3>
        <ul>
            <li><strong>icon16.png</strong> - 16x16 pixels (toolbar)</li>
            <li><strong>icon32.png</strong> - 32x32 pixels (Windows)</li>
            <li><strong>icon48.png</strong> - 48x48 pixels (extension management)</li>
            <li><strong>icon128.png</strong> - 128x128 pixels (Chrome Web Store)</li>
        </ul>
    </div>

    <script>
        function downloadIcon(svgId, size, filename) {
            const svg = document.getElementById(svgId);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            const svgData = new XMLSerializer().serializeToString(svg);
            const img = new Image();
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
        }
    </script>
</body>
</html>
