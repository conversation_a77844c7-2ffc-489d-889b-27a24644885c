// SecureVPN Pro - Background Service Worker

class VPNBackground {
  constructor() {
    this.isConnected = false;
    this.currentServer = null;
    this.proxyConfig = null;
    this.init();
  }

  init() {
    this.setupMessageListener();
    this.setupProxyErrorListener();
    this.setupWebRequestListener();
    this.loadSavedState();

    // Auto-connect if enabled
    this.checkAutoConnect();
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep the message channel open for async response
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'connect':
          const connectResult = await this.connect(message.server);
          sendResponse(connectResult);
          break;

        case 'disconnect':
          const disconnectResult = await this.disconnect();
          sendResponse(disconnectResult);
          break;

        case 'getStatus':
          sendResponse({
            connected: this.isConnected,
            server: this.currentServer
          });
          break;

        case 'updateWebRTCBlocking':
          await this.updateWebRTCBlocking(message.enabled);
          sendResponse({ success: true });
          break;

        case 'updateDNSProtection':
          await this.updateDNSProtection(message.enabled);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Message handling error:', error);
      sendResponse({ error: error.message });
    }
  }

  async connect(server) {
    try {
      if (this.isConnected) {
        await this.disconnect();
      }

      // Validate server configuration
      if (!this.validateServer(server)) {
        throw new Error('Invalid server configuration');
      }

      // Demo mode for testing
      if (server.id === 'demo-1' || server.host === '127.0.0.1') {
        console.log('Demo mode: Simulating connection to', server.name);

        // Simulate connection without actual proxy
        this.isConnected = true;
        this.currentServer = server;
        await this.saveState();
        this.updateIcon(true);
        await this.applySecuritySettings();

        return { success: true, demo: true };
      }

      // Create proxy configuration
      this.proxyConfig = this.createProxyConfig(server);

      // Set proxy
      await this.setProxy(this.proxyConfig);

      // Update state
      this.isConnected = true;
      this.currentServer = server;

      // Save state
      await this.saveState();

      // Update icon
      this.updateIcon(true);

      // Apply security settings
      await this.applySecuritySettings();

      console.log('Connected to VPN server:', server.name);

      return { success: true };
    } catch (error) {
      console.error('Connection failed:', error);
      return { success: false, error: error.message };
    }
  }

  async disconnect() {
    try {
      // Clear proxy
      await this.clearProxy();

      // Update state
      this.isConnected = false;
      this.currentServer = null;
      this.proxyConfig = null;

      // Save state
      await this.saveState();

      // Update icon
      this.updateIcon(false);

      console.log('Disconnected from VPN');

      return { success: true };
    } catch (error) {
      console.error('Disconnection failed:', error);
      return { success: false, error: error.message };
    }
  }

  validateServer(server) {
    return server &&
           server.host &&
           server.port &&
           server.type &&
           (server.type === 'socks5' || server.type === 'http') &&
           server.username &&
           server.password;
  }

  createProxyConfig(server) {
    const config = {
      mode: 'fixed_servers',
      rules: {}
    };

    if (server.type === 'socks5') {
      config.rules.singleProxy = {
        scheme: 'socks5',
        host: server.host,
        port: server.port
      };
    } else if (server.type === 'http') {
      config.rules.singleProxy = {
        scheme: 'http',
        host: server.host,
        port: server.port
      };
    }

    // Bypass local addresses
    config.rules.bypassList = [
      'localhost',
      '127.0.0.1',
      '::1',
      '*.local',
      '10.*',
      '172.16.*',
      '172.17.*',
      '172.18.*',
      '172.19.*',
      '172.20.*',
      '172.21.*',
      '172.22.*',
      '172.23.*',
      '172.24.*',
      '172.25.*',
      '172.26.*',
      '172.27.*',
      '172.28.*',
      '172.29.*',
      '172.30.*',
      '172.31.*',
      '192.168.*'
    ];

    return config;
  }

  async setProxy(config) {
    return new Promise((resolve, reject) => {
      chrome.proxy.settings.set(
        { value: config, scope: 'regular' },
        () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        }
      );
    });
  }

  async clearProxy() {
    return new Promise((resolve, reject) => {
      chrome.proxy.settings.clear(
        { scope: 'regular' },
        () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        }
      );
    });
  }

  setupProxyErrorListener() {
    chrome.proxy.onProxyError.addListener((details) => {
      console.error('Proxy error:', details);

      // If proxy fails, disconnect
      if (this.isConnected) {
        this.disconnect();
        this.showNotification('VPN Error', 'Connection lost due to proxy error');
      }
    });
  }

  setupWebRequestListener() {
    // Handle proxy authentication
    chrome.webRequest.onAuthRequired.addListener(
      (details) => {
        if (this.currentServer && this.isConnected) {
          return {
            authCredentials: {
              username: this.currentServer.username,
              password: this.currentServer.password
            }
          };
        }
        return {};
      },
      { urls: ['<all_urls>'] },
      ['blocking']
    );

    // Block WebRTC if enabled
    chrome.webRequest.onBeforeRequest.addListener(
      (details) => {
        return this.handleWebRTCBlocking(details);
      },
      { urls: ['*://*/*'] },
      ['blocking']
    );
  }

  async handleWebRTCBlocking(details) {
    const blockWebRTC = await this.getSetting('blockWebRTC', true);

    if (blockWebRTC && this.isConnected) {
      // Block WebRTC STUN/TURN requests
      if (details.url.includes('stun:') ||
          details.url.includes('turn:') ||
          details.url.includes('.stun.') ||
          details.url.includes('.turn.')) {
        return { cancel: true };
      }
    }

    return {};
  }

  async applySecuritySettings() {
    const blockWebRTC = await this.getSetting('blockWebRTC', true);
    const dnsProtection = await this.getSetting('dnsProtection', true);

    if (blockWebRTC) {
      await this.updateWebRTCBlocking(true);
    }

    if (dnsProtection) {
      await this.updateDNSProtection(true);
    }
  }

  async updateWebRTCBlocking(enabled) {
    await this.setSetting('blockWebRTC', enabled);

    // Inject content script to block WebRTC
    if (enabled && this.isConnected) {
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        try {
          await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: this.blockWebRTCFunction
          });
        } catch (error) {
          // Ignore errors for system pages
        }
      }
    }
  }

  blockWebRTCFunction() {
    // Override WebRTC APIs to prevent IP leaks
    if (typeof RTCPeerConnection !== 'undefined') {
      const originalRTCPeerConnection = RTCPeerConnection;
      RTCPeerConnection = function(config) {
        if (config && config.iceServers) {
          config.iceServers = [];
        }
        return new originalRTCPeerConnection(config);
      };
    }
  }

  async updateDNSProtection(enabled) {
    await this.setSetting('dnsProtection', enabled);

    if (enabled && this.isConnected) {
      // Use secure DNS servers
      const dnsRules = [
        {
          id: 1,
          priority: 1,
          action: { type: 'redirect', redirect: { url: 'https://*******/dns-query' } },
          condition: { urlFilter: '*://dns.google/*', resourceTypes: ['xmlhttprequest'] }
        }
      ];

      try {
        await chrome.declarativeNetRequest.updateDynamicRules({
          addRules: dnsRules,
          removeRuleIds: [1]
        });
      } catch (error) {
        console.error('Failed to update DNS rules:', error);
      }
    }
  }

  updateIcon(connected) {
    const iconPath = connected ? 'icons/icon-connected' : 'icons/icon';
    const title = connected ? 'SecureVPN Pro - Connected' : 'SecureVPN Pro - Disconnected';

    chrome.action.setIcon({
      path: {
        16: `${iconPath}16.png`,
        32: `${iconPath}32.png`,
        48: `${iconPath}48.png`,
        128: `${iconPath}128.png`
      }
    });

    chrome.action.setTitle({ title });
  }

  async showNotification(title, message) {
    try {
      await chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: title,
        message: message
      });
    } catch (error) {
      console.error('Notification error:', error);
    }
  }

  async saveState() {
    await chrome.storage.local.set({
      vpnConnected: this.isConnected,
      currentServer: this.currentServer
    });
  }

  async loadSavedState() {
    try {
      const result = await chrome.storage.local.get(['vpnConnected', 'currentServer']);

      if (result.vpnConnected && result.currentServer) {
        // Don't auto-reconnect on startup, just restore state
        this.isConnected = false;
        this.currentServer = result.currentServer;
      }
    } catch (error) {
      console.error('Failed to load saved state:', error);
    }
  }

  async checkAutoConnect() {
    const autoConnect = await this.getSetting('autoConnect', false);
    const selectedServer = await this.getSetting('selectedServer');

    if (autoConnect && selectedServer && !this.isConnected) {
      // Load servers and find the selected one
      try {
        const response = await fetch(chrome.runtime.getURL('servers.json'));
        const data = await response.json();
        const server = data.servers.find(s => s.id === selectedServer);

        if (server) {
          setTimeout(() => {
            this.connect(server);
          }, 2000); // Delay to ensure everything is loaded
        }
      } catch (error) {
        console.error('Auto-connect failed:', error);
      }
    }
  }

  async getSetting(key, defaultValue = null) {
    try {
      const result = await chrome.storage.local.get(key);
      return result[key] !== undefined ? result[key] : defaultValue;
    } catch (error) {
      console.error('Failed to get setting:', error);
      return defaultValue;
    }
  }

  async setSetting(key, value) {
    try {
      await chrome.storage.local.set({ [key]: value });
    } catch (error) {
      console.error('Failed to set setting:', error);
    }
  }
}

// Initialize the background service
new VPNBackground();
