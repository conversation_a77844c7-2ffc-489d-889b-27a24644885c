/* CSS Variables for theming */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

/* Dark theme */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --border-color: #334155;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  width: 380px;
  min-height: 600px;
  max-height: 600px;
  transition: all 0.3s ease;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 600px;
  max-height: 600px;
  overflow: hidden;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo h1 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-toggle {
  display: flex;
}

.theme-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.theme-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.theme-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* Status Section */
.status-section {
  padding: 1.5rem 1rem;
  text-align: center;
  background: var(--bg-primary);
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--danger-color);
  transition: all 0.3s ease;
  position: relative;
}

.status-indicator.connected {
  background: var(--success-color);
  animation: pulse 2s infinite;
}

.status-indicator.connecting {
  background: var(--warning-color);
  animation: blink 1s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.status-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

#statusText {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
}

.current-server {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.connect-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 120px;
}

.connect-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.connect-btn.connected {
  background: var(--danger-color);
}

.connect-btn.connecting {
  background: var(--warning-color);
  cursor: not-allowed;
}

.btn-loader {
  display: none;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.connect-btn.connecting .btn-loader {
  display: block;
}

.connect-btn.connecting .btn-text {
  opacity: 0;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Server Section */
.server-section {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  max-height: 200px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.refresh-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.refresh-btn svg {
  width: 16px;
  height: 16px;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.server-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.server-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.server-item:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.server-item.selected {
  border-color: var(--primary-color);
  background: rgba(79, 70, 229, 0.1);
}

.server-flag {
  font-size: 1.25rem;
  margin-right: 0.75rem;
}

.server-info {
  flex: 1;
}

.server-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: 0.125rem;
}

.server-location {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.server-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.125rem;
}

.server-ping {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.server-load {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* Settings Section */
.settings-section {
  padding: 1rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

.setting-item {
  margin-bottom: 0.75rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.setting-checkbox {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.2s ease;
}

.setting-checkbox:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.setting-checkbox:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Footer */
.footer {
  padding: 1rem;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
}

.footer-links {
  display: flex;
  justify-content: space-around;
}

.footer-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.footer-btn:hover {
  color: var(--primary-color);
}

/* Scrollbar */
.server-list::-webkit-scrollbar {
  width: 6px;
}

.server-list::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.server-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.server-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Toast animations */
@keyframes slideDown {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* Arabic/RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .header {
  flex-direction: row-reverse;
}

[dir="rtl"] .logo {
  flex-direction: row-reverse;
}

[dir="rtl"] .connection-status {
  flex-direction: row-reverse;
}

[dir="rtl"] .server-item {
  flex-direction: row-reverse;
}

[dir="rtl"] .server-flag {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .server-info {
  text-align: right;
}

[dir="rtl"] .server-stats {
  align-items: flex-start;
}

[dir="rtl"] .setting-label {
  flex-direction: row-reverse;
}

[dir="rtl"] .checkmark {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .section-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .footer-links {
  flex-direction: row-reverse;
}

/* Arabic Font Support */
.lang-ar {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

.lang-ar .logo h1 {
  font-weight: 700;
}

.lang-ar .server-name,
.lang-ar .setting-title,
.lang-ar .section-header h3 {
  font-weight: 600;
}

/* Language Selector */
.language-selector {
  position: relative;
  display: inline-block;
}

.language-btn {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0.5rem 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.language-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 120px;
  z-index: 1000;
  display: none;
}

.language-dropdown.show {
  display: block;
}

.language-option {
  padding: 0.75rem;
  cursor: pointer;
  transition: background 0.2s ease;
  font-size: 0.875rem;
}

.language-option:hover {
  background: var(--bg-secondary);
}

.language-option.active {
  background: var(--primary-color);
  color: white;
}

[dir="rtl"] .language-dropdown {
  right: auto;
  left: 0;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 320px;
  }

  .container {
    width: 320px;
  }

  .lang-ar .server-name {
    font-size: 0.8rem;
  }

  .lang-ar .setting-title {
    font-size: 0.85rem;
  }
}
